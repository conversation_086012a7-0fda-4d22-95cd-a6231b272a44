# Generated by Django 4.2.23 on 2025-07-28 08:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('audit', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='operationlog',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作用户'),
        ),
        migrations.AddIndex(
            model_name='operationlog',
            index=models.Index(fields=['user', 'created_at'], name='sys_operati_user_id_4d201e_idx'),
        ),
        migrations.AddIndex(
            model_name='operationlog',
            index=models.Index(fields=['operation_type', 'created_at'], name='sys_operati_operati_6a00ee_idx'),
        ),
    ]
