# Generated by Django 4.2.23 on 2025-07-28 08:26

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=100, verbose_name='部门名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='部门编码')),
                ('description', models.TextField(blank=True, verbose_name='部门描述')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
            ],
            options={
                'verbose_name': '部门',
                'verbose_name_plural': '部门',
                'db_table': 'sys_department',
            },
        ),
        migrations.CreateModel(
            name='UserDepartment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主部门')),
                ('is_manager', models.BooleanField(default=False, verbose_name='是否部门主管')),
                ('position', models.CharField(blank=True, max_length=100, verbose_name='职位')),
                ('manager_level', models.IntegerField(blank=True, choices=[(1, '一级主管'), (2, '二级主管'), (3, '三级主管')], null=True, verbose_name='主管级别')),
                ('weight', models.IntegerField(default=1, verbose_name='权重')),
                ('effective_date', models.DateField(default=django.utils.timezone.now, verbose_name='生效日期')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='失效日期')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='departments.department', verbose_name='部门')),
            ],
            options={
                'verbose_name': '用户部门关联',
                'verbose_name_plural': '用户部门关联',
                'db_table': 'sys_user_department',
            },
        ),
    ]
