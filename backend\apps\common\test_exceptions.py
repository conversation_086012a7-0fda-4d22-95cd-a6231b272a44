# -*- coding: utf-8 -*-
"""
异常处理模块单元测试
"""
import json
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from unittest.mock import Mock, patch

from .exceptions import (
    ErrorCode, 
    BusinessException, 
    custom_exception_handler,
    _get_error_code_from_status,
    _get_error_message_from_response
)
from .response import ApiResponse

User = get_user_model()


class ErrorCodeTestCase(TestCase):
    """错误码常量测试"""
    
    def test_error_code_constants(self):
        """测试错误码常量定义"""
        # 测试成功状态码
        self.assertEqual(ErrorCode.SUCCESS, 1000)
        
        # 测试认证错误码范围 (2000-2099)
        self.assertEqual(ErrorCode.LOGIN_FAILED, 2001)
        self.assertEqual(ErrorCode.CAPTCHA_ERROR, 2002)
        self.assertEqual(ErrorCode.TOKEN_EXPIRED, 2003)
        self.assertEqual(ErrorCode.TOKEN_INVALID, 2004)
        self.assertEqual(ErrorCode.ACCOUNT_LOCKED, 2005)
        self.assertEqual(ErrorCode.ACCOUNT_DISABLED, 2006)
        
        # 测试权限错误码范围 (2100-2199)
        self.assertEqual(ErrorCode.PERMISSION_DENIED, 2101)
        self.assertEqual(ErrorCode.INSUFFICIENT_PERMISSIONS, 2102)
        
        # 测试业务错误码范围 (3000-3999)
        self.assertEqual(ErrorCode.USER_NOT_FOUND, 3001)
        self.assertEqual(ErrorCode.USER_ALREADY_EXISTS, 3002)
        self.assertEqual(ErrorCode.DEPARTMENT_HAS_CHILDREN, 3003)
        self.assertEqual(ErrorCode.ROLE_IN_USE, 3004)
        
        # 测试系统错误码范围 (5000-5999)
        self.assertEqual(ErrorCode.INTERNAL_ERROR, 5000)
        self.assertEqual(ErrorCode.DATABASE_ERROR, 5001)
        self.assertEqual(ErrorCode.NETWORK_ERROR, 5002)


class BusinessExceptionTestCase(TestCase):
    """业务异常类测试"""
    
    def test_business_exception_creation(self):
        """测试业务异常创建"""
        # 测试默认参数
        exc = BusinessException()
        self.assertEqual(exc.code, ErrorCode.INTERNAL_ERROR)
        self.assertEqual(exc.message, "业务异常")
        self.assertIsNone(exc.data)
        
        # 测试自定义参数
        exc = BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在",
            data={"user_id": 123}
        )
        self.assertEqual(exc.code, ErrorCode.USER_NOT_FOUND)
        self.assertEqual(exc.message, "用户不存在")
        self.assertEqual(exc.data, {"user_id": 123})
    
    def test_business_exception_str(self):
        """测试业务异常字符串表示"""
        exc = BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在"
        )
        expected_str = f"BusinessException(code={ErrorCode.USER_NOT_FOUND}, message=用户不存在)"
        self.assertEqual(str(exc), expected_str)


class CustomExceptionHandlerTestCase(TestCase):
    """自定义异常处理器测试"""
    
    def setUp(self):
        """测试准备"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
    
    def test_business_exception_handling(self):
        """测试业务异常处理"""
        request = self.factory.get('/test/')
        request.user = self.user
        
        context = {
            'request': request,
            'view': Mock()
        }
        
        # 创建业务异常
        exc = BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在",
            data={"user_id": 123}
        )
        
        # 调用异常处理器
        response = custom_exception_handler(exc, context)
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.USER_NOT_FOUND)
        self.assertEqual(response.data['message'], "用户不存在")
        self.assertEqual(response.data['data'], {"user_id": 123})
        self.assertIn('timestamp', response.data)
    
    def test_validation_error_handling(self):
        """测试Django验证异常处理"""
        request = self.factory.get('/test/')
        request.user = self.user
        
        context = {
            'request': request,
            'view': Mock()
        }
        
        # 创建验证异常
        exc = ValidationError({
            'username': ['该字段不能为空'],
            'email': ['请输入有效的邮箱地址']
        })
        
        # 调用异常处理器
        response = custom_exception_handler(exc, context)
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.VALIDATION_ERROR)
        self.assertIn('username', response.data['message'])
        self.assertIn('email', response.data['message'])
    
    def test_integrity_error_handling(self):
        """测试数据库完整性异常处理"""
        request = self.factory.get('/test/')
        request.user = self.user
        
        context = {
            'request': request,
            'view': Mock()
        }
        
        # 创建完整性异常
        exc = IntegrityError("UNIQUE constraint failed")
        
        # 调用异常处理器
        response = custom_exception_handler(exc, context)
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.DATA_INTEGRITY_ERROR)
        self.assertIn('数据完整性错误', response.data['message'])
    
    @patch('apps.common.exceptions.exception_handler')
    def test_drf_exception_handling(self, mock_exception_handler):
        """测试DRF标准异常处理"""
        request = self.factory.get('/test/')
        request.user = self.user
        
        context = {
            'request': request,
            'view': Mock()
        }
        
        # 模拟DRF异常响应
        mock_response = Mock()
        mock_response.status_code = status.HTTP_404_NOT_FOUND
        mock_response.data = {'detail': '未找到'}
        mock_exception_handler.return_value = mock_response
        
        # 创建一般异常
        exc = Exception("测试异常")
        
        # 调用异常处理器
        response = custom_exception_handler(exc, context)
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['code'], ErrorCode.RESOURCE_NOT_FOUND)
        self.assertEqual(response.data['message'], '未找到')
        self.assertIn('timestamp', response.data)
    
    @patch('apps.common.exceptions.exception_handler')
    def test_unhandled_exception(self, mock_exception_handler):
        """测试未处理的系统异常"""
        request = self.factory.get('/test/')
        request.user = self.user
        
        context = {
            'request': request,
            'view': Mock()
        }
        
        # 模拟DRF异常处理器返回None
        mock_exception_handler.return_value = None
        
        # 创建未处理异常
        exc = Exception("未处理的异常")
        
        # 调用异常处理器
        response = custom_exception_handler(exc, context)
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data['code'], ErrorCode.INTERNAL_ERROR)
        self.assertIn('系统内部错误', response.data['message'])


class HelperFunctionsTestCase(TestCase):
    """辅助函数测试"""
    
    def test_get_error_code_from_status(self):
        """测试根据HTTP状态码获取错误码"""
        test_cases = [
            (400, ErrorCode.INVALID_PARAMETER),
            (401, ErrorCode.USER_NOT_AUTHENTICATED),
            (403, ErrorCode.PERMISSION_DENIED),
            (404, ErrorCode.RESOURCE_NOT_FOUND),
            (405, ErrorCode.OPERATION_NOT_ALLOWED),
            (500, ErrorCode.INTERNAL_ERROR),
            (502, ErrorCode.SERVICE_UNAVAILABLE),
            (503, ErrorCode.SERVICE_UNAVAILABLE),
            (504, ErrorCode.TIMEOUT_ERROR),
            (999, ErrorCode.INTERNAL_ERROR),  # 未知状态码
        ]
        
        for status_code, expected_code in test_cases:
            with self.subTest(status_code=status_code):
                result = _get_error_code_from_status(status_code)
                self.assertEqual(result, expected_code)
    
    def test_get_error_message_from_response(self):
        """测试从DRF响应中提取错误消息"""
        # 测试detail字段
        response = Mock()
        response.data = {'detail': '详细错误信息'}
        message = _get_error_message_from_response(response)
        self.assertEqual(message, '详细错误信息')
        
        # 测试多字段错误
        response.data = {
            'username': ['该字段不能为空'],
            'email': ['请输入有效的邮箱地址', '该邮箱已存在']
        }
        message = _get_error_message_from_response(response)
        self.assertIn('username', message)
        self.assertIn('email', message)
        
        # 测试列表格式错误
        response.data = ['错误1', '错误2']
        message = _get_error_message_from_response(response)
        self.assertEqual(message, '错误1; 错误2')
        
        # 测试空数据
        response.data = None
        message = _get_error_message_from_response(response)
        self.assertEqual(message, '请求处理失败')


class TestAPIView(APIView):
    """测试用的API视图"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # 根据查询参数决定抛出什么异常
        error_type = request.query_params.get('error_type')
        
        if error_type == 'business':
            raise BusinessException(
                code=ErrorCode.USER_NOT_FOUND,
                message="测试业务异常"
            )
        elif error_type == 'validation':
            raise ValidationError("测试验证异常")
        elif error_type == 'integrity':
            raise IntegrityError("测试完整性异常")
        elif error_type == 'system':
            raise Exception("测试系统异常")
        
        return Response({'message': '成功'})


class ExceptionHandlerIntegrationTestCase(APITestCase):
    """异常处理器集成测试"""
    
    def setUp(self):
        """测试准备"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_business_exception_integration(self):
        """测试业务异常集成"""
        # 注册测试视图
        from django.urls import path
        from django.conf.urls import include
        
        with patch('django.conf.urls.include') as mock_include:
            response = self.client.get('/test/?error_type=business')
            
            # 由于无法动态注册URL，这里模拟测试
            # 实际项目中应该在urls.py中注册测试视图
            pass
    
    def test_api_response_format(self):
        """测试API响应格式"""
        # 测试成功响应
        response = ApiResponse.success(
            data={'id': 1, 'name': '测试'},
            message='查询成功'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertEqual(response.data['message'], '查询成功')
        self.assertEqual(response.data['data'], {'id': 1, 'name': '测试'})
        self.assertIn('timestamp', response.data)
        
        # 测试错误响应
        response = ApiResponse.error(
            message='测试错误',
            code=ErrorCode.USER_NOT_FOUND
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.USER_NOT_FOUND)
        self.assertEqual(response.data['message'], '测试错误')
        self.assertIsNone(response.data['data'])
        self.assertIn('timestamp', response.data)
        
        # 测试分页响应
        page_info = {
            'count': 100,
            'page': 1,
            'page_size': 20,
            'total_pages': 5
        }
        response = ApiResponse.paginated_success(
            data=[{'id': 1}, {'id': 2}],
            page_info=page_info
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertEqual(len(response.data['data']['results']), 2)
        self.assertEqual(response.data['data']['pagination'], page_info)


class ExceptionLoggingTestCase(TestCase):
    """异常日志记录测试"""
    
    def setUp(self):
        """测试准备"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
    
    @patch('apps.common.exceptions.logger')
    def test_exception_logging(self, mock_logger):
        """测试异常日志记录"""
        request = self.factory.get('/test/')
        request.user = self.user
        
        context = {
            'request': request,
            'view': Mock()
        }
        
        # 创建业务异常
        exc = BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在"
        )
        
        # 调用异常处理器
        custom_exception_handler(exc, context)
        
        # 验证日志记录
        mock_logger.error.assert_called()
        call_args = mock_logger.error.call_args[0][0]
        self.assertIn('API异常', call_args)
        self.assertIn('/test/', call_args)
        self.assertIn('GET', call_args)
        self.assertIn('testuser', call_args)
        self.assertIn('BusinessException', call_args)
    
    @patch('apps.common.exceptions.logger')
    def test_system_exception_logging(self, mock_logger):
        """测试系统异常日志记录"""
        request = self.factory.get('/test/')
        request.user = self.user
        
        context = {
            'request': request,
            'view': Mock()
        }
        
        # 创建系统异常
        exc = Exception("系统异常")
        
        with patch('apps.common.exceptions.exception_handler', return_value=None):
            # 调用异常处理器
            custom_exception_handler(exc, context)
        
        # 验证日志记录（应该记录两次：一次API异常，一次未处理异常）
        self.assertEqual(mock_logger.error.call_count, 2)