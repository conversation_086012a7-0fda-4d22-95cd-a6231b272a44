"""
部门管理模块 - 序列化器
"""
from rest_framework import serializers
from django.utils import timezone
from apps.common.exceptions import BusinessException, ErrorCode
from .models import Department, UserDepartment


class DepartmentSerializer(serializers.ModelSerializer):
    """部门序列化器 - 基础CRUD操作"""
    
    # 只读字段
    id = serializers.IntegerField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    updated_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    level = serializers.IntegerField(read_only=True, help_text="部门层级")
    lft = serializers.IntegerField(read_only=True, help_text="MPTT左值")
    rght = serializers.IntegerField(read_only=True, help_text="MPTT右值")
    tree_id = serializers.IntegerField(read_only=True, help_text="MPTT树ID")
    
    # 父部门信息
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    
    # 统计信息
    children_count = serializers.SerializerMethodField()
    member_count = serializers.SerializerMethodField()
    manager_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'parent', 'parent_name', 'description', 
            'sort_order', 'is_active', 'level', 'lft', 'rght', 'tree_id',
            'children_count', 'member_count', 'manager_count',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'name': {'required': True},
            'code': {'required': True},
            'parent': {'required': False, 'allow_null': True},
            'description': {'required': False},
            'sort_order': {'default': 0},
            'is_active': {'default': True},
        }
    
    def get_children_count(self, obj):
        """获取子部门数量"""
        return obj.get_children().filter(is_deleted=False).count()
    
    def get_member_count(self, obj):
        """获取部门成员数量（包括兼职）"""
        return UserDepartment.get_effective_relations(department=obj).count()
    
    def get_manager_count(self, obj):
        """获取部门主管数量"""
        return obj.get_managers().count()
    
    def validate_code(self, value):
        """验证部门编码唯一性"""
        queryset = Department.objects.filter(code=value)
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError("部门编码已存在")
        return value
    
    def validate_parent(self, value):
        """验证父部门"""
        if value:
            # 检查父部门是否存在且未被删除
            if value.is_deleted:
                raise serializers.ValidationError("父部门已被删除")
            
            # 更新时检查是否会形成循环引用
            if self.instance:
                if value == self.instance:
                    raise serializers.ValidationError("不能将自己设为父部门")
                
                # 检查是否会形成循环引用
                if self.instance.is_ancestor_of(value):
                    raise serializers.ValidationError("不能将子部门设为父部门")
        
        return value


class DepartmentTreeSerializer(serializers.ModelSerializer):
    """部门树形结构序列化器"""
    
    children = serializers.SerializerMethodField()
    member_count = serializers.SerializerMethodField()
    manager_info = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'description', 'sort_order', 
            'is_active', 'level', 'member_count', 'manager_info', 'children'
        ]
    
    def get_children(self, obj):
        """获取子部门"""
        children = obj.get_children().filter(is_deleted=False).order_by('sort_order', 'name')
        return DepartmentTreeSerializer(children, many=True, context=self.context).data
    
    def get_member_count(self, obj):
        """获取部门成员数量"""
        return UserDepartment.get_effective_relations(department=obj).count()
    
    def get_manager_info(self, obj):
        """获取部门主管信息"""
        managers = obj.get_managers()
        return [{
            'user_id': rel.user.id,
            'username': rel.user.username,
            'nickname': rel.user.nickname,
            'manager_level': rel.manager_level,
            'weight': rel.weight,
            'position': rel.position
        } for rel in managers]


class DepartmentListSerializer(serializers.ModelSerializer):
    """部门列表序列化器 - 简化字段"""
    
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    member_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'parent', 'parent_name', 
            'is_active', 'level', 'member_count', 'created_at'
        ]
        read_only_fields = fields
    
    def get_member_count(self, obj):
        """获取部门成员数量"""
        return UserDepartment.get_effective_relations(department=obj).count()


class UserDepartmentSerializer(serializers.ModelSerializer):
    """用户部门关联序列化器"""
    
    # 用户信息
    user_info = serializers.SerializerMethodField()
    # 部门信息
    department_info = serializers.SerializerMethodField()
    # 有效性状态
    is_effective_now = serializers.SerializerMethodField()
    
    class Meta:
        model = UserDepartment
        fields = [
            'id', 'user', 'department', 'user_info', 'department_info',
            'is_primary', 'is_manager', 'position', 'manager_level', 'weight',
            'effective_date', 'expiry_date', 'is_effective_now',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'user': {'required': True},
            'department': {'required': True},
            'is_primary': {'default': False},
            'is_manager': {'default': False},
            'position': {'required': False},
            'manager_level': {'required': False, 'allow_null': True},
            'weight': {'default': 1},
            'effective_date': {'default': timezone.now().date},
            'expiry_date': {'required': False, 'allow_null': True},
        }
    
    def get_user_info(self, obj):
        """获取用户信息"""
        return {
            'id': obj.user.id,
            'username': obj.user.username,
            'nickname': obj.user.nickname,
            'email': obj.user.email,
            'is_active': obj.user.is_active
        }
    
    def get_department_info(self, obj):
        """获取部门信息"""
        return {
            'id': obj.department.id,
            'name': obj.department.name,
            'code': obj.department.code,
            'level': obj.department.level
        }
    
    def get_is_effective_now(self, obj):
        """获取当前是否有效"""
        return obj.is_effective()
    
    def validate(self, attrs):
        """验证数据"""
        user = attrs.get('user')
        department = attrs.get('department')
        is_primary = attrs.get('is_primary', False)
        is_manager = attrs.get('is_manager', False)
        manager_level = attrs.get('manager_level')
        effective_date = attrs.get('effective_date')
        expiry_date = attrs.get('expiry_date')
        
        # 验证用户和部门是否存在且未被删除
        if user and user.is_deleted:
            raise serializers.ValidationError({'user': '用户已被删除'})
        
        if department and department.is_deleted:
            raise serializers.ValidationError({'department': '部门已被删除'})
        
        # 验证主管级别
        if is_manager and not manager_level:
            raise serializers.ValidationError({'manager_level': '设为主管时必须指定主管级别'})
        
        if not is_manager and manager_level:
            raise serializers.ValidationError({'manager_level': '非主管不能设置主管级别'})
        
        # 验证日期
        if effective_date and expiry_date and effective_date >= expiry_date:
            raise serializers.ValidationError({'expiry_date': '失效日期必须晚于生效日期'})
        
        # 验证主部门唯一性（创建时）
        if is_primary and not self.instance:
            existing_primary = UserDepartment.objects.filter(
                user=user,
                is_primary=True,
                is_deleted=False
            ).exists()
            if existing_primary:
                raise serializers.ValidationError({'is_primary': '用户已有主部门，不能重复设置'})
        
        # 验证用户部门关联唯一性
        if not self.instance:
            existing_relation = UserDepartment.objects.filter(
                user=user,
                department=department,
                is_deleted=False
            ).exists()
            if existing_relation:
                raise serializers.ValidationError('用户已在该部门中')
        
        return attrs


class DepartmentMemberSerializer(serializers.Serializer):
    """部门成员管理序列化器"""
    
    user_id = serializers.IntegerField(help_text="用户ID")
    is_primary = serializers.BooleanField(default=False, help_text="是否为主部门")
    is_manager = serializers.BooleanField(default=False, help_text="是否为主管")
    position = serializers.CharField(max_length=100, required=False, allow_blank=True, help_text="职位")
    manager_level = serializers.IntegerField(required=False, allow_null=True, help_text="主管级别")
    weight = serializers.IntegerField(default=1, help_text="权重")
    effective_date = serializers.DateField(default=timezone.now().date, help_text="生效日期")
    expiry_date = serializers.DateField(required=False, allow_null=True, help_text="失效日期")
    
    def validate_user_id(self, value):
        """验证用户ID"""
        from apps.users.models import UserProfile
        try:
            user = UserProfile.objects.get(id=value, is_deleted=False)
            return value
        except UserProfile.DoesNotExist:
            raise serializers.ValidationError("用户不存在或已被删除")
    
    def validate(self, attrs):
        """验证数据"""
        is_manager = attrs.get('is_manager', False)
        manager_level = attrs.get('manager_level')
        effective_date = attrs.get('effective_date')
        expiry_date = attrs.get('expiry_date')
        
        # 验证主管级别
        if is_manager and not manager_level:
            raise serializers.ValidationError({'manager_level': '设为主管时必须指定主管级别'})
        
        if not is_manager and manager_level:
            raise serializers.ValidationError({'manager_level': '非主管不能设置主管级别'})
        
        # 验证日期
        if effective_date and expiry_date and effective_date >= expiry_date:
            raise serializers.ValidationError({'expiry_date': '失效日期必须晚于生效日期'})
        
        return attrs


class DepartmentPathSerializer(serializers.ModelSerializer):
    """部门路径序列化器 - 用于显示部门完整路径"""
    
    path = serializers.SerializerMethodField()
    ancestors = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = ['id', 'name', 'code', 'level', 'path', 'ancestors']
    
    def get_path(self, obj):
        """获取部门完整路径"""
        ancestors = obj.get_ancestors(include_self=True)
        return ' > '.join([dept.name for dept in ancestors])
    
    def get_ancestors(self, obj):
        """获取祖先部门列表"""
        ancestors = obj.get_ancestors()
        return [{
            'id': dept.id,
            'name': dept.name,
            'code': dept.code,
            'level': dept.level
        } for dept in ancestors]
