"""
用户管理模块 - 单元测试
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from apps.common.exceptions import ErrorCode
from .models import UserProfile

User = get_user_model()


class UserModelTestCase(TestCase):
    """用户模型测试"""
    
    def setUp(self):
        """测试数据准备"""
        self.user_data = {
            'username': 'testuser',
            'nickname': '测试用户',
            'email': '<EMAIL>',
            'phone': '13800138000',
            'password': 'testpass123'
        }
    
    def test_create_user(self):
        """测试创建用户"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.nickname, '测试用户')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('testpass123'))
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
    
    def test_create_superuser(self):
        """测试创建超级用户"""
        user = User.objects.create_superuser(
            username='admin',
            nickname='管理员',
            email='<EMAIL>',
            password='adminpass123'
        )
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
    
    def test_soft_delete(self):
        """测试软删除"""
        user = User.objects.create_user(**self.user_data)
        user.soft_delete()
        
        self.assertTrue(user.is_deleted)
        self.assertIsNotNone(user.deleted_at)
        
        # 验证活跃管理器不包含已删除用户
        self.assertFalse(User.objects.filter(id=user.id).exists())
        # 验证全对象管理器包含已删除用户
        self.assertTrue(User.all_objects.filter(id=user.id).exists())
    
    def test_restore_user(self):
        """测试恢复用户"""
        user = User.objects.create_user(**self.user_data)
        user.soft_delete()
        user.restore()
        
        self.assertFalse(user.is_deleted)
        self.assertIsNone(user.deleted_at)
        
        # 验证用户重新出现在活跃管理器中
        self.assertTrue(User.objects.filter(id=user.id).exists())
    
    def test_user_str_representation(self):
        """测试用户字符串表示"""
        user = User.objects.create_user(**self.user_data)
        expected = f"{user.nickname} ({user.username})"
        self.assertEqual(str(user), expected)


class UserAPITestCase(APITestCase):
    """用户API测试"""
    
    def setUp(self):
        """测试数据准备"""
        self.client = APIClient()
        
        # 创建测试用户
        self.admin_user = User.objects.create_user(
            username='admin',
            nickname='管理员',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )
        
        self.normal_user = User.objects.create_user(
            username='user',
            nickname='普通用户',
            email='<EMAIL>',
            password='userpass123'
        )
        
        # 获取JWT令牌
        self.admin_token = self._get_jwt_token(self.admin_user)
        self.user_token = self._get_jwt_token(self.normal_user)
    
    def _get_jwt_token(self, user):
        """获取JWT令牌"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def _authenticate_as_admin(self):
        """以管理员身份认证"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
    
    def _authenticate_as_user(self):
        """以普通用户身份认证"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')
    
    def test_get_user_list(self):
        """测试获取用户列表"""
        self._authenticate_as_admin()
        url = reverse('user-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        self.assertIn('data', response.data)

        # 验证分页响应格式
        data = response.data['data']
        if 'results' in data:
            # 有分页时的格式
            self.assertIn('results', data)
            self.assertIn('pagination', data)
            self.assertIn('count', data['pagination'])
            self.assertIn('page', data['pagination'])
            self.assertIn('page_size', data['pagination'])
            self.assertIn('total_pages', data['pagination'])
        else:
            # 无分页时的格式（直接是用户列表）
            self.assertIsInstance(data, list)
    
    def test_get_user_list_with_search(self):
        """测试搜索用户列表"""
        self._authenticate_as_admin()
        url = reverse('user-list')
        response = self.client.get(url, {'search': 'admin'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # 验证搜索结果包含admin用户
        data = response.data['data']
        users = data['results'] if 'results' in data else data
        admin_found = any(user['username'] == 'admin' for user in users)
        self.assertTrue(admin_found)
    
    def test_create_user(self):
        """测试创建用户"""
        self._authenticate_as_admin()
        url = reverse('user-list')
        data = {
            'username': 'newuser',
            'nickname': '新用户',
            'email': '<EMAIL>',
            'password': 'newpass123',
            'password_confirm': 'newpass123'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1001)  # 创建成功码
        
        # 验证用户已创建
        user = User.objects.get(username='newuser')
        self.assertEqual(user.nickname, '新用户')
        self.assertTrue(user.check_password('newpass123'))
    
    def test_create_user_duplicate_username(self):
        """测试创建重复用户名的用户"""
        self._authenticate_as_admin()
        url = reverse('user-list')
        data = {
            'username': 'admin',  # 已存在的用户名
            'nickname': '重复用户',
            'email': '<EMAIL>',
            'password': 'duppass123',
            'password_confirm': 'duppass123'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_create_user_password_mismatch(self):
        """测试密码不匹配"""
        self._authenticate_as_admin()
        url = reverse('user-list')
        data = {
            'username': 'mismatchuser',
            'nickname': '密码不匹配用户',
            'email': '<EMAIL>',
            'password': 'password123',
            'password_confirm': 'different123'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_get_user_detail(self):
        """测试获取用户详情"""
        self._authenticate_as_admin()
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        self.assertEqual(response.data['data']['username'], 'user')
    
    def test_update_user(self):
        """测试更新用户信息"""
        self._authenticate_as_admin()
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        data = {
            'nickname': '更新后的昵称',
            'email': '<EMAIL>'
        }
        response = self.client.patch(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证更新成功
        self.normal_user.refresh_from_db()
        self.assertEqual(self.normal_user.nickname, '更新后的昵称')
        self.assertEqual(self.normal_user.email, '<EMAIL>')
    
    def test_delete_user(self):
        """测试软删除用户"""
        self._authenticate_as_admin()
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证用户被软删除
        self.normal_user.refresh_from_db()
        self.assertTrue(self.normal_user.is_deleted)
    
    def test_cannot_delete_self(self):
        """测试不能删除自己"""
        self._authenticate_as_admin()
        url = reverse('user-detail', kwargs={'pk': self.admin_user.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.CANNOT_DELETE_SELF)
    
    def test_restore_user(self):
        """测试恢复用户"""
        # 先软删除用户
        self.normal_user.soft_delete()
        
        self._authenticate_as_admin()
        url = reverse('user-restore', kwargs={'pk': self.normal_user.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证用户已恢复
        self.normal_user.refresh_from_db()
        self.assertFalse(self.normal_user.is_deleted)
    
    def test_toggle_user_active(self):
        """测试切换用户激活状态"""
        self._authenticate_as_admin()
        url = reverse('user-toggle-active', kwargs={'pk': self.normal_user.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证状态已切换
        self.normal_user.refresh_from_db()
        self.assertFalse(self.normal_user.is_active)
    
    def test_reset_password(self):
        """测试重置密码"""
        self._authenticate_as_admin()
        url = reverse('user-reset-password', kwargs={'pk': self.normal_user.id})
        data = {
            'new_password': 'newpassword123',
            'new_password_confirm': 'newpassword123'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证密码已更改
        self.normal_user.refresh_from_db()
        self.assertTrue(self.normal_user.check_password('newpassword123'))
    
    def test_get_profile(self):
        """测试获取当前用户信息"""
        self._authenticate_as_user()
        url = reverse('user-profile')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        self.assertEqual(response.data['data']['username'], 'user')
    
    def test_update_profile(self):
        """测试更新个人资料"""
        self._authenticate_as_user()
        url = reverse('user-update-profile')
        data = {
            'nickname': '更新的昵称',
            'email': '<EMAIL>'
        }
        response = self.client.patch(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证更新成功
        self.normal_user.refresh_from_db()
        self.assertEqual(self.normal_user.nickname, '更新的昵称')
    
    def test_change_password(self):
        """测试修改密码"""
        self._authenticate_as_user()
        url = reverse('user-change-password')
        data = {
            'old_password': 'userpass123',
            'new_password': 'newuserpass123',
            'new_password_confirm': 'newuserpass123'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证密码已更改
        self.normal_user.refresh_from_db()
        self.assertTrue(self.normal_user.check_password('newuserpass123'))
    
    def test_change_password_wrong_old_password(self):
        """测试修改密码时原密码错误"""
        self._authenticate_as_user()
        url = reverse('user-change-password')
        data = {
            'old_password': 'wrongpassword',
            'new_password': 'newuserpass123',
            'new_password_confirm': 'newuserpass123'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_unauthorized_access(self):
        """测试未认证访问"""
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
