"""
公共模块 - 基础模型和管理器
"""
from django.db import models
from django.utils import timezone


class BaseModel(models.Model):
    """基础模型 - 提供通用字段和软删除功能"""
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    is_deleted = models.BooleanField(default=False, verbose_name="是否删除")
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name="删除时间")
    
    class Meta:
        abstract = True
    
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()
    
    def restore(self):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None
        self.save()


class ActiveManager(models.Manager):
    """活跃对象管理器 - 过滤已删除对象"""
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)