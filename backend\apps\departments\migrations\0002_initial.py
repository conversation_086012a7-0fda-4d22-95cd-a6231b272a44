# Generated by Django 4.2.23 on 2025-07-28 08:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import mptt.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('departments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='userdepartment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='department',
            name='parent',
            field=mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='departments.department', verbose_name='上级部门'),
        ),
        migrations.AddIndex(
            model_name='userdepartment',
            index=models.Index(fields=['user', 'is_manager'], name='sys_user_de_user_id_840811_idx'),
        ),
        migrations.AddIndex(
            model_name='userdepartment',
            index=models.Index(fields=['department', 'is_manager'], name='sys_user_de_departm_9b22a4_idx'),
        ),
        migrations.AddIndex(
            model_name='userdepartment',
            index=models.Index(fields=['effective_date', 'expiry_date'], name='sys_user_de_effecti_6733c2_idx'),
        ),
        migrations.AddConstraint(
            model_name='userdepartment',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False), ('is_primary', True)), fields=('user',), name='unique_primary_department_per_user'),
        ),
        migrations.AlterUniqueTogether(
            name='userdepartment',
            unique_together={('user', 'department')},
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['parent', 'is_active'], name='sys_departm_parent__38429a_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['code'], name='sys_departm_code_177833_idx'),
        ),
    ]
