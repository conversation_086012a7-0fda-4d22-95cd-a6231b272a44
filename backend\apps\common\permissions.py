"""
数据范围权限服务
"""
from django.db.models import Q
from django.utils import timezone
from apps.departments.models import Department, UserDepartment


class DataScopeService:
    """数据范围权限服务"""
    
    @staticmethod
    def get_data_scope_filter(user, data_scope):
        """根据数据范围获取查询过滤器"""
        if data_scope == 'ALL':
            return Q()  # 无限制
        
        elif data_scope == 'SELF_ONLY':
            return Q(created_by=user)
        
        elif data_scope == 'DEPT_ONLY':
            # 获取用户有效的主部门
            primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                is_primary=True
            ).first()
            if primary_dept:
                return Q(department=primary_dept.department)
            return Q(pk__in=[])  # 无部门则无数据
        
        elif data_scope == 'DEPT_AND_SUB':
            # 获取用户有效的主部门及其子部门
            primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                is_primary=True
            ).first()
            if primary_dept:
                dept_ids = Department.objects.get_descendants(
                    primary_dept.department, include_self=True
                ).values_list('id', flat=True)
                return Q(department_id__in=dept_ids)
            return Q(pk__in=[])
        
        return Q(pk__in=[])  # 默认无数据
    
    @staticmethod
    def get_user_departments(user, include_expired=False):
        """获取用户所属部门"""
        queryset = UserDepartment.objects.filter(user=user, is_deleted=False)
        
        if not include_expired:
            queryset = UserDepartment.get_effective_relations(user=user)
        
        return queryset.select_related('department')
    
    @staticmethod
    def get_user_managed_departments(user, level=None):
        """获取用户管理的部门"""
        queryset = UserDepartment.get_effective_relations(user=user).filter(
            is_manager=True
        )
        
        if level:
            queryset = queryset.filter(manager_level=level)
        
        return queryset.select_related('department').order_by('manager_level', 'weight')

    @staticmethod
    def get_user_roles_with_data_scope(user):
        """获取用户角色及其数据范围"""
        from apps.permissions.models import UserRole

        user_roles = UserRole.objects.filter(
            user=user,
            is_deleted=False
        ).select_related('role', 'department')

        roles_data = []
        for user_role in user_roles:
            roles_data.append({
                'role': user_role.role,
                'department': user_role.department,
                'data_scope': user_role.role.data_scope,
            })

        return roles_data

    @staticmethod
    def apply_data_scope_to_queryset(queryset, user, data_scope_field='department'):
        """将数据范围过滤应用到查询集"""
        # 获取用户的最高数据范围权限
        user_roles = DataScopeService.get_user_roles_with_data_scope(user)

        if not user_roles:
            return queryset.filter(pk__in=[])  # 无角色则无数据

        # 获取最宽泛的数据范围
        data_scopes = [role['data_scope'] for role in user_roles]

        if 'ALL' in data_scopes:
            return queryset  # 有全部数据权限

        # 构建过滤条件
        filters = Q()

        for scope in data_scopes:
            scope_filter = DataScopeService.get_data_scope_filter(user, scope)
            filters |= scope_filter

        return queryset.filter(filters)

    @staticmethod
    def check_data_access_permission(user, obj, data_scope_field='department'):
        """检查用户是否有权限访问特定数据对象"""
        user_roles = DataScopeService.get_user_roles_with_data_scope(user)

        if not user_roles:
            return False

        # 检查是否有全部数据权限
        data_scopes = [role['data_scope'] for role in user_roles]
        if 'ALL' in data_scopes:
            return True

        # 检查具体权限
        for role_data in user_roles:
            scope = role_data['data_scope']

            if scope == 'SELF_ONLY':
                if hasattr(obj, 'created_by') and obj.created_by == user:
                    return True

            elif scope == 'DEPT_ONLY':
                primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                    is_primary=True
                ).first()
                if primary_dept and hasattr(obj, data_scope_field):
                    obj_dept = getattr(obj, data_scope_field)
                    if obj_dept == primary_dept.department:
                        return True

            elif scope == 'DEPT_AND_SUB':
                primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                    is_primary=True
                ).first()
                if primary_dept and hasattr(obj, data_scope_field):
                    obj_dept = getattr(obj, data_scope_field)
                    dept_ids = Department.objects.get_descendants(
                        primary_dept.department, include_self=True
                    ).values_list('id', flat=True)
                    if obj_dept.id in dept_ids:
                        return True

        return False
