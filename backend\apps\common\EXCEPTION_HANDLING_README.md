# 统一异常处理机制文档

## 概述

本模块实现了HEIM企业管理平台的统一API响应格式和异常处理机制，提供了一致的错误处理和响应格式，提升了API的可维护性和用户体验。

## 核心组件

### 1. ApiResponse 统一响应格式类

位置：`apps/common/response.py`

提供三种响应格式：

#### 成功响应
```python
ApiResponse.success(
    data={"id": 1, "name": "张三"},
    message="操作成功",
    code=1000
)
```

响应格式：
```json
{
    "code": 1000,
    "message": "操作成功",
    "data": {"id": 1, "name": "张三"},
    "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### 错误响应
```python
ApiResponse.error(
    message="用户不存在",
    code=3001,
    status_code=400
)
```

响应格式：
```json
{
    "code": 3001,
    "message": "用户不存在",
    "data": null,
    "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### 分页响应
```python
ApiResponse.paginated_success(
    data=[{"id": 1}, {"id": 2}],
    page_info={
        "count": 100,
        "page": 1,
        "page_size": 10,
        "total_pages": 10
    }
)
```

响应格式：
```json
{
    "code": 1000,
    "message": "查询成功",
    "data": {
        "results": [{"id": 1}, {"id": 2}],
        "pagination": {
            "count": 100,
            "page": 1,
            "page_size": 10,
            "total_pages": 10
        }
    },
    "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 2. ErrorCode 错误码常量

位置：`apps/common/exceptions.py`

#### 错误码分类

| 范围 | 类型 | 说明 |
|------|------|------|
| 1000 | 成功 | 操作成功 |
| 2000-2099 | 认证错误 | 登录、令牌相关错误 |
| 2100-2199 | 权限错误 | 权限验证相关错误 |
| 3000-3999 | 业务错误 | 业务逻辑相关错误 |
| 5000-5999 | 系统错误 | 系统内部错误 |

#### 常用错误码

**认证错误 (2000-2099)**
- `LOGIN_FAILED = 2001` - 登录失败
- `CAPTCHA_ERROR = 2002` - 验证码错误
- `TOKEN_EXPIRED = 2003` - 令牌过期
- `TOKEN_INVALID = 2004` - 令牌无效
- `ACCOUNT_LOCKED = 2005` - 账户锁定
- `ACCOUNT_DISABLED = 2006` - 账户禁用

**权限错误 (2100-2199)**
- `PERMISSION_DENIED = 2101` - 权限拒绝
- `INSUFFICIENT_PERMISSIONS = 2102` - 权限不足
- `ACCESS_FORBIDDEN = 2103` - 访问禁止

**业务错误 (3000-3999)**
- `USER_NOT_FOUND = 3001` - 用户不存在
- `USER_ALREADY_EXISTS = 3002` - 用户已存在
- `DEPARTMENT_HAS_CHILDREN = 3003` - 部门有子部门
- `ROLE_IN_USE = 3004` - 角色正在使用
- `VALIDATION_ERROR = 3010` - 数据验证错误

**系统错误 (5000-5999)**
- `INTERNAL_ERROR = 5000` - 内部错误
- `DATABASE_ERROR = 5001` - 数据库错误
- `NETWORK_ERROR = 5002` - 网络错误

### 3. BusinessException 业务异常类

位置：`apps/common/exceptions.py`

用于处理业务逻辑中的异常情况：

```python
# 基本用法
raise BusinessException(
    code=ErrorCode.USER_NOT_FOUND,
    message="用户不存在",
    data={"user_id": 123}
)

# 简化用法
raise BusinessException(
    code=ErrorCode.PERMISSION_DENIED,
    message="您没有权限执行此操作"
)
```

### 4. custom_exception_handler 全局异常处理器

位置：`apps/common/exceptions.py`

自动处理以下类型的异常：

1. **BusinessException** - 业务异常，返回统一格式的错误响应
2. **ValidationError** - Django验证异常，提取字段错误信息
3. **IntegrityError** - 数据库完整性异常，返回友好的错误信息
4. **DRF标准异常** - 处理DRF框架的标准异常
5. **未捕获异常** - 记录日志并返回通用错误响应

#### 异常处理流程

```mermaid
graph TD
    A[API请求] --> B[视图处理]
    B --> C{是否发生异常?}
    C -->|否| D[正常响应]
    C -->|是| E[异常处理器]
    E --> F{异常类型}
    F -->|BusinessException| G[业务异常处理]
    F -->|ValidationError| H[验证异常处理]
    F -->|IntegrityError| I[完整性异常处理]
    F -->|DRF异常| J[DRF异常处理]
    F -->|其他异常| K[系统异常处理]
    G --> L[统一错误响应]
    H --> L
    I --> L
    J --> L
    K --> L
    L --> M[记录日志]
    M --> N[返回响应]
```

## 配置说明

### Django REST Framework 配置

在 `config/settings/base.py` 中配置：

```python
REST_FRAMEWORK = {
    'EXCEPTION_HANDLER': 'apps.common.exceptions.custom_exception_handler',
    # 其他配置...
}
```

### 日志配置

异常处理器会自动记录异常日志，包含以下信息：
- 请求路径和方法
- 用户信息
- 异常类型和消息
- 完整的异常堆栈

## 使用示例

### 在视图中使用

```python
from rest_framework.views import APIView
from apps.common.exceptions import BusinessException, ErrorCode
from apps.common.response import ApiResponse

class UserViewSet(APIView):
    def get(self, request, pk):
        try:
            user = User.objects.get(pk=pk)
        except User.DoesNotExist:
            raise BusinessException(
                code=ErrorCode.USER_NOT_FOUND,
                message="用户不存在",
                data={"user_id": pk}
            )
        
        return ApiResponse.success(
            data={"id": user.id, "name": user.name},
            message="查询成功"
        )
    
    def post(self, request):
        username = request.data.get('username')
        if not username:
            raise BusinessException(
                code=ErrorCode.VALIDATION_ERROR,
                message="用户名不能为空",
                data={"field": "username"}
            )
        
        if User.objects.filter(username=username).exists():
            raise BusinessException(
                code=ErrorCode.USER_ALREADY_EXISTS,
                message="用户名已存在"
            )
        
        # 创建用户逻辑...
        return ApiResponse.success(message="用户创建成功")
```

### 在服务层使用

```python
from apps.common.exceptions import BusinessException, ErrorCode

class UserService:
    @staticmethod
    def create_user(username, email):
        # 验证用户名
        if len(username) < 3:
            raise BusinessException(
                code=ErrorCode.VALIDATION_ERROR,
                message="用户名长度不能少于3个字符",
                data={"field": "username", "min_length": 3}
            )
        
        # 检查重复
        if User.objects.filter(username=username).exists():
            raise BusinessException(
                code=ErrorCode.USER_ALREADY_EXISTS,
                message="用户名已存在，请选择其他用户名"
            )
        
        # 创建用户...
        return user
```

## 测试

### 运行单元测试

```bash
# 运行异常处理相关测试
uv run python manage.py test apps.common.test_exceptions -v 2

# 运行基本功能测试
uv run python simple_test.py
```

### 测试覆盖

- ✅ 错误码常量定义
- ✅ 业务异常类功能
- ✅ 统一响应格式
- ✅ 全局异常处理器
- ✅ 不同类型异常处理
- ✅ 日志记录功能
- ✅ 辅助函数功能

## 演示端点

为了演示异常处理功能，提供了以下测试端点：

### 1. 异常演示端点
```
GET /api/common/demo/exception/
GET /api/common/demo/exception/?error_type=business
GET /api/common/demo/exception/?error_type=validation
GET /api/common/demo/exception/?error_type=system
```

### 2. 用户创建演示
```
POST /api/common/demo/create-user/
Content-Type: application/json

{
    "username": "testuser",
    "email": "<EMAIL>"
}
```

### 3. 分页演示
```
GET /api/common/demo/paginated/?page=1&page_size=10
```

## 最佳实践

### 1. 异常抛出
- 使用具体的错误码，避免使用通用错误码
- 提供清晰的错误消息，帮助用户理解问题
- 在data字段中提供有用的调试信息

### 2. 错误码管理
- 按功能模块分配错误码范围
- 保持错误码的唯一性和一致性
- 定期审查和更新错误码文档

### 3. 日志记录
- 异常处理器会自动记录日志，无需手动记录
- 敏感信息不要包含在异常消息中
- 使用data字段传递结构化的错误信息

### 4. 前端集成
- 前端应根据code字段判断处理逻辑
- message字段可直接显示给用户
- data字段包含额外的处理信息

## 扩展说明

### 添加新的错误码
1. 在 `ErrorCode` 类中添加新的常量
2. 选择合适的错误码范围
3. 更新文档说明

### 自定义异常处理
如需特殊的异常处理逻辑，可以在 `custom_exception_handler` 中添加：

```python
def custom_exception_handler(exc, context):
    # 处理自定义异常类型
    if isinstance(exc, MyCustomException):
        return ApiResponse.error(
            message=exc.message,
            code=exc.code
        )
    
    # 调用默认处理逻辑
    return default_handler(exc, context)
```

## 总结

统一异常处理机制提供了：

1. **一致的API响应格式** - 所有API返回统一的JSON格式
2. **完善的错误码体系** - 分类清晰的错误码定义
3. **自动异常处理** - 全局异常处理器自动处理各种异常
4. **详细的日志记录** - 自动记录异常信息便于调试
5. **易于使用的接口** - 简单的API设计，易于开发使用

这套机制为HEIM企业管理平台提供了稳定可靠的错误处理基础，提升了系统的健壮性和用户体验。