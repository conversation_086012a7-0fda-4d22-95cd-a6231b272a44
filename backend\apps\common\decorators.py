"""
公共模块 - 装饰器
"""
from functools import wraps
from django.http import JsonResponse
from django.utils import timezone
from rest_framework import status
from apps.common.permissions import DataScopeService
import logging

logger = logging.getLogger(__name__)


def require_data_scope(data_scope_level, data_scope_field='department'):
    """
    数据范围权限验证装饰器
    
    Args:
        data_scope_level (str): 所需的数据范围级别
        data_scope_field (str): 数据范围字段名
    
    Usage:
        @require_data_scope('ALL')
        def get_all_users(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否已认证
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                return JsonResponse({
                    'code': 2003,
                    'message': '用户未认证',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # 超级管理员跳过权限检查
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # 检查数据范围权限
            user_data_scope = DataScopeService.get_user_data_scope(request.user)
            
            # 数据范围权限级别
            scope_levels = {
                'SELF_ONLY': 1,
                'DEPT_ONLY': 2,
                'DEPT_AND_SUB': 3,
                'ALL': 4,
                'CUSTOM': 0
            }
            
            user_level = scope_levels.get(user_data_scope, 0)
            required_level = scope_levels.get(data_scope_level, 4)
            
            if user_level < required_level:
                return JsonResponse({
                    'code': 2101,
                    'message': f'数据范围权限不足，当前权限: {user_data_scope}，需要权限: {data_scope_level}',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_403_FORBIDDEN)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def apply_data_scope_filter(data_scope_field='department', custom_rules=None):
    """
    自动应用数据范围过滤的装饰器
    
    Args:
        data_scope_field (str): 数据范围字段名
        custom_rules (dict): 自定义规则
    
    Usage:
        @apply_data_scope_filter('department')
        def get_users(request):
            queryset = User.objects.all()
            # 装饰器会自动应用数据范围过滤
            return queryset
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 执行原始视图函数
            result = view_func(request, *args, **kwargs)
            
            # 如果返回的是QuerySet，应用数据范围过滤
            if hasattr(result, 'filter') and hasattr(request, 'user') and request.user.is_authenticated:
                try:
                    result = DataScopeService.apply_data_scope_to_queryset(
                        result, request.user, data_scope_field, custom_rules
                    )
                except Exception as e:
                    logger.error(f"应用数据范围过滤失败: {e}")
            
            return result
        return wrapper
    return decorator


class DataScopeMixin:
    """数据范围权限混入类（用于类视图）"""
    
    data_scope_field = 'department'  # 数据范围字段
    required_data_scope = None  # 必需的数据范围
    custom_rules = None  # 自定义规则
    auto_apply_filter = True  # 是否自动应用数据范围过滤
    
    def check_data_scope_permission(self, request):
        """检查数据范围权限"""
        # 检查用户是否已认证
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return False, '用户未认证'
        
        # 超级管理员跳过权限检查
        if request.user.is_superuser:
            return True, ''
        
        # 检查数据范围权限
        if self.required_data_scope:
            user_data_scope = DataScopeService.get_user_data_scope(request.user)
            
            scope_levels = {
                'SELF_ONLY': 1,
                'DEPT_ONLY': 2,
                'DEPT_AND_SUB': 3,
                'ALL': 4,
                'CUSTOM': 0
            }
            
            user_level = scope_levels.get(user_data_scope, 0)
            required_level = scope_levels.get(self.required_data_scope, 4)
            
            if user_level < required_level:
                return False, f'数据范围权限不足，当前权限: {user_data_scope}，需要权限: {self.required_data_scope}'
        
        return True, ''
    
    def apply_data_scope_filter(self, queryset, request):
        """应用数据范围过滤"""
        if not self.auto_apply_filter:
            return queryset
        
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                return DataScopeService.apply_data_scope_to_queryset(
                    queryset, request.user, self.data_scope_field, self.custom_rules
                )
            except Exception as e:
                logger.error(f"应用数据范围过滤失败: {e}")
                # 异常情况下返回空查询集，确保数据安全
                return queryset.filter(pk__in=[])
        
        return queryset
    
    def get_queryset(self):
        """重写get_queryset方法，自动应用数据范围过滤"""
        queryset = super().get_queryset()
        
        if hasattr(self, 'request') and self.auto_apply_filter:
            queryset = self.apply_data_scope_filter(queryset, self.request)
        
        return queryset
    
    def dispatch(self, request, *args, **kwargs):
        """重写dispatch方法，添加数据范围权限检查"""
        has_permission, error_message = self.check_data_scope_permission(request)
        
        if not has_permission:
            status_code = status.HTTP_401_UNAUTHORIZED if '未认证' in error_message else status.HTTP_403_FORBIDDEN
            error_code = 2003 if '未认证' in error_message else 2101
            
            return JsonResponse({
                'code': error_code,
                'message': error_message,
                'data': None,
                'timestamp': timezone.now().isoformat()
            }, status=status_code)
        
        return super().dispatch(request, *args, **kwargs)


def check_object_access(data_scope_field='department', custom_rules=None):
    """
    检查对象访问权限的装饰器
    
    Args:
        data_scope_field (str): 数据范围字段名
        custom_rules (dict): 自定义规则
    
    Usage:
        @check_object_access('department')
        def get_user_detail(request, user_id):
            user = get_object_or_404(User, id=user_id)
            # 装饰器会自动检查访问权限
            return user
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 执行原始视图函数
            result = view_func(request, *args, **kwargs)
            
            # 如果返回的是模型实例，检查访问权限
            if hasattr(result, '_meta') and hasattr(request, 'user') and request.user.is_authenticated:
                try:
                    has_access = DataScopeService.check_data_access_permission(
                        request.user, result, data_scope_field, custom_rules
                    )
                    
                    if not has_access:
                        return JsonResponse({
                            'code': 2101,
                            'message': '无权访问该数据',
                            'data': None,
                            'timestamp': timezone.now().isoformat()
                        }, status=status.HTTP_403_FORBIDDEN)
                        
                except Exception as e:
                    logger.error(f"检查对象访问权限失败: {e}")
                    return JsonResponse({
                        'code': 5000,
                        'message': '权限检查异常',
                        'data': None,
                        'timestamp': timezone.now().isoformat()
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return result
        return wrapper
    return decorator
