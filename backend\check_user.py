#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.users.models import UserProfile

try:
    user = UserProfile.objects.get(username='testuser')
    print(f'用户存在: {user.username}, 活跃状态: {user.is_active}')
    print(f'密码检查: {user.check_password("testpass123")}')
    print(f'失败次数: {user.login_fail_count}')
    print(f'锁定状态: {user.locked_until}')
except UserProfile.DoesNotExist:
    print('用户不存在，正在创建...')
    user = UserProfile.objects.create_user(
        username='testuser',
        password='testpass123',
        nickname='测试用户',
        email='<EMAIL>'
    )
    print(f'用户已创建: {user.username}')
