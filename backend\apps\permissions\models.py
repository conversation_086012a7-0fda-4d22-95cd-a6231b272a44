"""
权限管理模块 - 角色和权限模型
"""
from django.db import models
from apps.common.models import BaseModel, ActiveManager


class Role(BaseModel):
    """角色模型"""
    DATA_SCOPE_CHOICES = [
        ('ALL', '全部数据'),
        ('DEPT_AND_SUB', '本部门及下级'),
        ('DEPT_ONLY', '仅本部门'),
        ('SELF_ONLY', '仅本人'),
        ('CUSTOM', '自定义'),
    ]
    
    name = models.CharField(max_length=100, verbose_name="角色名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="角色编码")
    data_scope = models.CharField(max_length=20, choices=DATA_SCOPE_CHOICES, 
                                 default='SELF_ONLY', verbose_name="数据范围")
    
    # 角色属性
    description = models.TextField(blank=True, verbose_name="角色描述")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    
    # 关联
    permissions = models.ManyToManyField('Permission', blank=True, verbose_name="权限")
    users = models.ManyToManyField('users.UserProfile', through='UserRole', verbose_name="用户")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_role'
        verbose_name = "角色"
        verbose_name_plural = "角色"
    
    def __str__(self):
        return self.name


class Permission(BaseModel):
    """权限模型"""
    PERMISSION_TYPE_CHOICES = [
        ('MENU', '菜单权限'),
        ('BUTTON', '按钮权限'),
        ('API', 'API权限'),
    ]
    
    name = models.CharField(max_length=100, verbose_name="权限名称")
    code = models.CharField(max_length=100, unique=True, verbose_name="权限编码")
    permission_type = models.CharField(max_length=10, choices=PERMISSION_TYPE_CHOICES, 
                                     default='MENU', verbose_name="权限类型")
    
    # 权限属性
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, 
                              related_name='children', verbose_name="父权限")
    path = models.CharField(max_length=500, blank=True, verbose_name="路径")
    component = models.CharField(max_length=200, blank=True, verbose_name="组件")
    icon = models.CharField(max_length=100, blank=True, verbose_name="图标")
    
    # HTTP方法字段
    http_method = models.CharField(max_length=10, blank=True, verbose_name="HTTP方法")
    
    # 其他属性
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_permission'
        verbose_name = "权限"
        verbose_name_plural = "权限"
        indexes = [
            models.Index(fields=['permission_type', 'is_active']),
            models.Index(fields=['parent', 'sort_order']),
        ]
    
    def __str__(self):
        return self.name


class UserRole(BaseModel):
    """用户角色关联"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name="角色")
    department = models.ForeignKey('departments.Department', on_delete=models.CASCADE, 
                                  null=True, blank=True, verbose_name="关联部门")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_user_role'
        unique_together = ['user', 'role', 'department']
        verbose_name = "用户角色关联"
        verbose_name_plural = "用户角色关联"
    
    def __str__(self):
        dept_str = f" ({self.department.name})" if self.department else ""
        return f"{self.user.nickname} - {self.role.name}{dept_str}"