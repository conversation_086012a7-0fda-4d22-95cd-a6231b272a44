"""
权限管理模块 - 单元测试
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.permissions.models import Role, Permission, UserRole
from apps.permissions.services import PermissionService, RoleService
from apps.departments.models import Department, UserDepartment

User = get_user_model()


class PermissionModelTest(TestCase):
    """权限模型测试"""
    
    def setUp(self):
        self.permission = Permission.objects.create(
            name="用户管理",
            code="user:manage",
            permission_type="MENU",
            path="/users",
            component="UserManage",
            icon="user"
        )
    
    def test_permission_creation(self):
        """测试权限创建"""
        self.assertEqual(self.permission.name, "用户管理")
        self.assertEqual(self.permission.code, "user:manage")
        self.assertEqual(self.permission.permission_type, "MENU")
        self.assertTrue(self.permission.is_active)
        self.assertFalse(self.permission.is_deleted)
    
    def test_permission_str(self):
        """测试权限字符串表示"""
        self.assertEqual(str(self.permission), "用户管理")
    
    def test_permission_soft_delete(self):
        """测试权限软删除"""
        self.permission.soft_delete()
        self.assertTrue(self.permission.is_deleted)
        self.assertIsNotNone(self.permission.deleted_at)


class RoleModelTest(TestCase):
    """角色模型测试"""
    
    def setUp(self):
        self.role = Role.objects.create(
            name="管理员",
            code="admin",
            data_scope="ALL",
            description="系统管理员"
        )
        self.permission = Permission.objects.create(
            name="用户管理",
            code="user:manage",
            permission_type="MENU"
        )
    
    def test_role_creation(self):
        """测试角色创建"""
        self.assertEqual(self.role.name, "管理员")
        self.assertEqual(self.role.code, "admin")
        self.assertEqual(self.role.data_scope, "ALL")
        self.assertTrue(self.role.is_active)
        self.assertFalse(self.role.is_deleted)
    
    def test_role_permissions(self):
        """测试角色权限关联"""
        self.role.permissions.add(self.permission)
        self.assertIn(self.permission, self.role.permissions.all())
    
    def test_role_str(self):
        """测试角色字符串表示"""
        self.assertEqual(str(self.role), "管理员")


class UserRoleModelTest(TestCase):
    """用户角色关联模型测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass123",
            nickname="测试用户"
        )
        self.role = Role.objects.create(
            name="普通用户",
            code="user",
            data_scope="SELF_ONLY"
        )
        self.department = Department.objects.create(
            name="技术部",
            code="tech"
        )
    
    def test_user_role_creation(self):
        """测试用户角色关联创建"""
        user_role = UserRole.objects.create(
            user=self.user,
            role=self.role,
            department=self.department
        )
        self.assertEqual(user_role.user, self.user)
        self.assertEqual(user_role.role, self.role)
        self.assertEqual(user_role.department, self.department)
    
    def test_user_role_str(self):
        """测试用户角色关联字符串表示"""
        user_role = UserRole.objects.create(
            user=self.user,
            role=self.role,
            department=self.department
        )
        expected = f"{self.user.nickname} - {self.role.name} ({self.department.name})"
        self.assertEqual(str(user_role), expected)


class PermissionServiceTest(TestCase):
    """权限服务测试"""
    
    def setUp(self):
        # 创建用户
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass123",
            nickname="测试用户"
        )
        
        # 创建部门
        self.department = Department.objects.create(
            name="技术部",
            code="tech"
        )
        
        # 创建用户部门关联
        UserDepartment.objects.create(
            user=self.user,
            department=self.department,
            is_primary=True
        )
        
        # 创建权限
        self.permission1 = Permission.objects.create(
            name="用户查看",
            code="user:view",
            permission_type="MENU"
        )
        self.permission2 = Permission.objects.create(
            name="用户创建",
            code="user:create",
            permission_type="BUTTON"
        )
        
        # 创建角色
        self.role = Role.objects.create(
            name="普通用户",
            code="user",
            data_scope="DEPT_ONLY"
        )
        self.role.permissions.set([self.permission1, self.permission2])
        
        # 创建用户角色关联
        UserRole.objects.create(
            user=self.user,
            role=self.role,
            department=self.department
        )
    
    def test_get_user_permissions(self):
        """测试获取用户权限"""
        permissions = PermissionService.get_user_permissions(self.user)
        permission_codes = [p.code for p in permissions]
        self.assertIn("user:view", permission_codes)
        self.assertIn("user:create", permission_codes)
    
    def test_get_user_roles(self):
        """测试获取用户角色"""
        roles = PermissionService.get_user_roles(self.user)
        role_codes = [r.code for r in roles]
        self.assertIn("user", role_codes)
    
    def test_get_user_data_scope(self):
        """测试获取用户数据范围"""
        data_scope = PermissionService.get_user_data_scope(self.user)
        self.assertEqual(data_scope, "DEPT_ONLY")
    
    def test_check_user_permission(self):
        """测试检查用户权限"""
        self.assertTrue(PermissionService.check_user_permission(self.user, "user:view"))
        self.assertTrue(PermissionService.check_user_permission(self.user, "user:create"))
        self.assertFalse(PermissionService.check_user_permission(self.user, "user:delete"))
    
    def test_get_user_menu_permissions(self):
        """测试获取用户菜单权限"""
        menu_permissions = PermissionService.get_user_menu_permissions(self.user)
        self.assertIsInstance(menu_permissions, list)
    
    def test_get_user_button_permissions(self):
        """测试获取用户按钮权限"""
        button_permissions = PermissionService.get_user_button_permissions(self.user)
        self.assertIn("user:create", button_permissions)


class RoleServiceTest(TestCase):
    """角色服务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass123",
            nickname="测试用户"
        )
        self.role = Role.objects.create(
            name="测试角色",
            code="test",
            data_scope="SELF_ONLY"
        )
    
    def test_check_role_can_delete_with_users(self):
        """测试检查有用户的角色是否可删除"""
        UserRole.objects.create(user=self.user, role=self.role)
        can_delete, message = RoleService.check_role_can_delete(self.role)
        self.assertFalse(can_delete)
        self.assertIn("正在被用户使用", message)
    
    def test_check_role_can_delete_without_users(self):
        """测试检查无用户的角色是否可删除"""
        can_delete, message = RoleService.check_role_can_delete(self.role)
        self.assertTrue(can_delete)
        self.assertEqual(message, "")
    
    def test_get_role_users(self):
        """测试获取角色用户"""
        UserRole.objects.create(user=self.user, role=self.role)
        user_roles = RoleService.get_role_users(self.role)
        self.assertEqual(user_roles.count(), 1)
        self.assertEqual(user_roles.first().user, self.user)


class PermissionAPITest(TestCase):
    """权限API测试"""

    def setUp(self):
        # 在测试中禁用权限中间件
        from django.test.utils import override_settings
        self.settings_override = override_settings(
            MIDDLEWARE=[
                'django.middleware.security.SecurityMiddleware',
                'corsheaders.middleware.CorsMiddleware',
                'django.contrib.sessions.middleware.SessionMiddleware',
                'django.middleware.common.CommonMiddleware',
                'django.middleware.csrf.CsrfViewMiddleware',
                'django.contrib.auth.middleware.AuthenticationMiddleware',
                'django.contrib.messages.middleware.MessageMiddleware',
                'django.middleware.clickjacking.XFrameOptionsMiddleware',
                'apps.common.middleware.JWTAuthenticationMiddleware',
                # 'apps.common.middleware.PermissionMiddleware',  # 测试时禁用
                'apps.common.middleware.AuditLogMiddleware',
            ]
        )
        self.settings_override.enable()
        
        self.client = APIClient()
        self.user = User.objects.create_superuser(
            username="testuser",
            password="testpass123",
            nickname="测试用户",
            email="<EMAIL>"
        )
        # 确保用户是超级用户
        self.user.is_superuser = True
        self.user.save()
        self.client.force_authenticate(user=self.user)
        
        self.permission = Permission.objects.create(
            name="测试权限",
            code="test:permission",
            permission_type="MENU"
        )
    
    def test_permission_list(self):
        """测试权限列表API"""
        url = reverse('permission-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_permission_create(self):
        """测试权限创建API"""
        url = reverse('permission-list')
        data = {
            'name': '新权限',
            'code': 'new:permission',
            'permission_type': 'BUTTON'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)  # 成功码
    
    def test_permission_tree(self):
        """测试权限树API"""
        url = reverse('permission-tree')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class RoleAPITest(TestCase):
    """角色API测试"""

    def setUp(self):
        # 在测试中禁用权限中间件
        from django.test.utils import override_settings
        self.settings_override = override_settings(
            MIDDLEWARE=[
                'django.middleware.security.SecurityMiddleware',
                'corsheaders.middleware.CorsMiddleware',
                'django.contrib.sessions.middleware.SessionMiddleware',
                'django.middleware.common.CommonMiddleware',
                'django.middleware.csrf.CsrfViewMiddleware',
                'django.contrib.auth.middleware.AuthenticationMiddleware',
                'django.contrib.messages.middleware.MessageMiddleware',
                'django.middleware.clickjacking.XFrameOptionsMiddleware',
                'apps.common.middleware.JWTAuthenticationMiddleware',
                # 'apps.common.middleware.PermissionMiddleware',  # 测试时禁用
                'apps.common.middleware.AuditLogMiddleware',
            ]
        )
        self.settings_override.enable()
        
        self.client = APIClient()
        self.user = User.objects.create_superuser(
            username="testuser",
            password="testpass123",
            nickname="测试用户",
            email="<EMAIL>"
        )
        # 确保用户是超级用户
        self.user.is_superuser = True
        self.user.save()
        self.client.force_authenticate(user=self.user)
        
        self.role = Role.objects.create(
            name="测试角色",
            code="test:role",
            data_scope="SELF_ONLY"
        )
    
    def test_role_list(self):
        """测试角色列表API"""
        url = reverse('role-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_role_create(self):
        """测试角色创建API"""
        url = reverse('role-list')
        data = {
            'name': '新角色',
            'code': 'new:role',
            'data_scope': 'DEPT_ONLY',
            'description': '新角色描述'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)  # 成功码
    
    def test_role_data_scopes(self):
        """测试角色数据范围选项API"""
        url = reverse('role-data-scopes')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
