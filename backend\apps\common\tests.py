# -*- coding: utf-8 -*-
"""
公共模块单元测试
"""
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from unittest.mock import Mock, patch

from .response import ApiResponse
from .exceptions import BusinessException, ErrorCode, custom_exception_handler


class ApiResponseTestCase(TestCase):
    """ApiResponse统一响应格式测试"""
    
    def test_success_response(self):
        """测试成功响应格式"""
        data = {'id': 1, 'name': 'test'}
        response = ApiResponse.success(data=data, message="测试成功")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertEqual(response.data['message'], "测试成功")
        self.assertEqual(response.data['data'], data)
        self.assertIn('timestamp', response.data)
    
    def test_success_response_with_custom_code(self):
        """测试自定义成功码的响应"""
        response = ApiResponse.success(message="自定义成功", code=1001)
        
        self.assertEqual(response.data['code'], 1001)
        self.assertEqual(response.data['message'], "自定义成功")
    
    def test_error_response(self):
        """测试错误响应格式"""
        response = ApiResponse.error(
            message="测试错误", 
            code=ErrorCode.INVALID_PARAMETER
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.INVALID_PARAMETER)
        self.assertEqual(response.data['message'], "测试错误")
        self.assertIsNone(response.data['data'])
        self.assertIn('timestamp', response.data)
    
    def test_error_response_with_custom_status(self):
        """测试自定义HTTP状态码的错误响应"""
        response = ApiResponse.error(
            message="未找到资源",
            code=ErrorCode.RESOURCE_NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['code'], ErrorCode.RESOURCE_NOT_FOUND)
    
    def test_paginated_success_response(self):
        """测试分页成功响应格式"""
        data = [{'id': 1}, {'id': 2}]
        page_info = {
            'count': 2,
            'page': 1,
            'page_size': 10,
            'total_pages': 1
        }
        
        response = ApiResponse.paginated_success(data, page_info)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertEqual(response.data['data']['results'], data)
        self.assertEqual(response.data['data']['pagination'], page_info)


class ErrorCodeTestCase(TestCase):
    """错误码常量测试"""
    
    def test_error_code_constants(self):
        """测试错误码常量定义"""
        # 成功码
        self.assertEqual(ErrorCode.SUCCESS, 1000)
        
        # 认证错误码
        self.assertEqual(ErrorCode.LOGIN_FAILED, 2001)
        self.assertEqual(ErrorCode.TOKEN_EXPIRED, 2003)
        self.assertEqual(ErrorCode.ACCOUNT_LOCKED, 2005)
        
        # 权限错误码
        self.assertEqual(ErrorCode.PERMISSION_DENIED, 2101)
        self.assertEqual(ErrorCode.INSUFFICIENT_PERMISSIONS, 2102)
        
        # 业务错误码
        self.assertEqual(ErrorCode.USER_NOT_FOUND, 3001)
        self.assertEqual(ErrorCode.INVALID_PARAMETER, 3005)
        
        # 系统错误码
        self.assertEqual(ErrorCode.INTERNAL_ERROR, 5000)
        self.assertEqual(ErrorCode.DATABASE_ERROR, 5001)


class BusinessExceptionTestCase(TestCase):
    """BusinessException自定义异常测试"""
    
    def test_business_exception_creation(self):
        """测试业务异常创建"""
        exc = BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在",
            data={'user_id': 123}
        )
        
        self.assertEqual(exc.code, ErrorCode.USER_NOT_FOUND)
        self.assertEqual(exc.message, "用户不存在")
        self.assertEqual(exc.data, {'user_id': 123})
        self.assertEqual(str(exc), "BusinessException(code=3001, message=用户不存在)")
    
    def test_business_exception_default_values(self):
        """测试业务异常默认值"""
        exc = BusinessException()
        
        self.assertEqual(exc.code, ErrorCode.INTERNAL_ERROR)
        self.assertEqual(exc.message, "业务异常")
        self.assertIsNone(exc.data)
    
    def test_business_exception_inheritance(self):
        """测试业务异常继承关系"""
        exc = BusinessException(message="测试异常")
        self.assertIsInstance(exc, Exception)
        self.assertEqual(str(exc), "BusinessException(code=5000, message=测试异常)")


class CustomExceptionHandlerTestCase(TestCase):
    """自定义异常处理器测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.mock_request = Mock()
        self.mock_request.path = '/api/test/'
        self.mock_request.method = 'GET'
        self.mock_request.user.username = 'testuser'
        
        self.context = {
            'request': self.mock_request,
            'view': Mock()
        }
    
    def test_handle_business_exception(self):
        """测试处理业务异常"""
        exc = BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在",
            data={'user_id': 123}
        )
        
        response = custom_exception_handler(exc, self.context)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.USER_NOT_FOUND)
        self.assertEqual(response.data['message'], "用户不存在")
        self.assertEqual(response.data['data'], {'user_id': 123})
        self.assertIn('timestamp', response.data)
    
    def test_handle_validation_error_with_message_dict(self):
        """测试处理Django验证错误（字段错误）"""
        exc = ValidationError({
            'username': ['用户名不能为空'],
            'email': ['邮箱格式不正确', '邮箱已存在']
        })
        
        response = custom_exception_handler(exc, self.context)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.VALIDATION_ERROR)
        self.assertIn('username:', response.data['message'])
        self.assertIn('email:', response.data['message'])
    
    def test_handle_validation_error_with_messages(self):
        """测试处理Django验证错误（非字段错误）"""
        exc = ValidationError(['密码不能与用户名相同', '密码强度不够'])
        
        response = custom_exception_handler(exc, self.context)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.VALIDATION_ERROR)
        self.assertIn('密码不能与用户名相同', response.data['message'])
        self.assertIn('密码强度不够', response.data['message'])
    
    def test_handle_integrity_error(self):
        """测试处理数据库完整性错误"""
        exc = IntegrityError("UNIQUE constraint failed: users_userprofile.username")
        
        response = custom_exception_handler(exc, self.context)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.DATA_INTEGRITY_ERROR)
        self.assertIn('数据完整性错误', response.data['message'])
    
    @patch('apps.common.exceptions.exception_handler')
    def test_handle_drf_exception(self, mock_drf_handler):
        """测试处理DRF标准异常"""
        # 模拟DRF异常处理器返回的响应
        mock_response = Mock()
        mock_response.status_code = status.HTTP_404_NOT_FOUND
        mock_response.data = {'detail': '未找到'}
        mock_drf_handler.return_value = mock_response
        
        exc = Exception("测试异常")
        response = custom_exception_handler(exc, self.context)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['code'], ErrorCode.RESOURCE_NOT_FOUND)
        self.assertEqual(response.data['message'], '未找到')
        self.assertIn('timestamp', response.data)
    
    @patch('apps.common.exceptions.exception_handler')
    def test_handle_unhandled_exception(self, mock_drf_handler):
        """测试处理未捕获的系统异常"""
        mock_drf_handler.return_value = None  # DRF处理器无法处理
        
        exc = Exception("未知系统异常")
        response = custom_exception_handler(exc, self.context)
        
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data['code'], ErrorCode.INTERNAL_ERROR)
        self.assertEqual(response.data['message'], "系统内部错误，请稍后重试")
    
    def test_handle_exception_without_request(self):
        """测试处理没有请求上下文的异常"""
        context = {}  # 空上下文
        exc = BusinessException(message="测试异常")
        
        # 应该不会抛出异常，正常处理
        response = custom_exception_handler(exc, context)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['message'], "测试异常")


class ExceptionHandlerIntegrationTestCase(APITestCase):
    """异常处理器集成测试"""
    
    def test_api_business_exception_response(self):
        """测试API中业务异常的响应格式"""
        # 这里需要创建一个测试视图来触发异常
        # 由于这是集成测试，我们可以通过实际的API调用来测试
        pass  # 实际项目中会有具体的API端点来测试
    
    def test_api_validation_error_response(self):
        """测试API中验证错误的响应格式"""
        pass  # 实际项目中会有具体的API端点来测试
    
    def test_api_permission_denied_response(self):
        """测试API中权限拒绝的响应格式"""
        pass  # 实际项目中会有具体的API端点来测试


class UtilsTestCase(TestCase):
    """工具函数测试"""
    
    def test_raise_business_error(self):
        """测试抛出业务异常的便捷函数"""
        from .utils import raise_business_error
        
        with self.assertRaises(BusinessException) as context:
            raise_business_error(
                code=ErrorCode.USER_NOT_FOUND,
                message="用户不存在",
                data={'user_id': 123}
            )
        
        exc = context.exception
        self.assertEqual(exc.code, ErrorCode.USER_NOT_FOUND)
        self.assertEqual(exc.message, "用户不存在")
        self.assertEqual(exc.data, {'user_id': 123})
    
    def test_validate_required_fields_success(self):
        """测试必填字段验证成功"""
        from .utils import validate_required_fields
        
        data = {'username': 'test', 'email': '<EMAIL>'}
        required_fields = ['username', 'email']
        
        # 应该不抛出异常
        validate_required_fields(data, required_fields)
    
    def test_validate_required_fields_missing(self):
        """测试必填字段验证失败"""
        from .utils import validate_required_fields
        
        data = {'username': 'test'}
        required_fields = ['username', 'email', 'password']
        
        with self.assertRaises(BusinessException) as context:
            validate_required_fields(data, required_fields)
        
        exc = context.exception
        self.assertEqual(exc.code, ErrorCode.INVALID_PARAMETER)
        self.assertIn('email', exc.message)
        self.assertIn('password', exc.message)
    
    def test_validate_required_fields_empty_values(self):
        """测试必填字段空值验证"""
        from .utils import validate_required_fields
        
        data = {'username': '', 'email': None, 'password': 'test'}
        required_fields = ['username', 'email', 'password']
        
        with self.assertRaises(BusinessException) as context:
            validate_required_fields(data, required_fields)
        
        exc = context.exception
        self.assertIn('username', exc.message)
        self.assertIn('email', exc.message)
    
    def test_validate_user_exists_success(self):
        """测试用户存在验证成功"""
        from .utils import validate_user_exists
        
        mock_user = Mock()
        # 应该不抛出异常
        validate_user_exists(mock_user)
    
    def test_validate_user_exists_failure(self):
        """测试用户存在验证失败"""
        from .utils import validate_user_exists
        
        with self.assertRaises(BusinessException) as context:
            validate_user_exists(None)
        
        exc = context.exception
        self.assertEqual(exc.code, ErrorCode.USER_NOT_FOUND)
        self.assertEqual(exc.message, "用户不存在")
    
    def test_validate_user_active_success(self):
        """测试用户激活状态验证成功"""
        from .utils import validate_user_active
        
        mock_user = Mock()
        mock_user.is_active = True
        
        # 应该不抛出异常
        validate_user_active(mock_user)
    
    def test_validate_user_active_failure(self):
        """测试用户激活状态验证失败"""
        from .utils import validate_user_active
        
        mock_user = Mock()
        mock_user.is_active = False
        
        with self.assertRaises(BusinessException) as context:
            validate_user_active(mock_user)
        
        exc = context.exception
        self.assertEqual(exc.code, ErrorCode.ACCOUNT_DISABLED)
        self.assertEqual(exc.message, "用户账户已被禁用")


class ResponseHelperTestCase(TestCase):
    """响应助手类测试"""
    
    def test_success_with_data(self):
        """测试带数据的成功响应"""
        from .utils import ResponseHelper
        
        data = {'id': 1, 'name': 'test'}
        response = ResponseHelper.success_with_data(data, "查询成功")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertEqual(response.data['message'], "查询成功")
        self.assertEqual(response.data['data'], data)
    
    def test_success_without_data(self):
        """测试不带数据的成功响应"""
        from .utils import ResponseHelper
        
        response = ResponseHelper.success_without_data("操作完成")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertEqual(response.data['message'], "操作完成")
        self.assertIsNone(response.data['data'])
    
    def test_created_success(self):
        """测试创建成功响应"""
        from .utils import ResponseHelper
        
        data = {'id': 1}
        response = ResponseHelper.created_success(data)
        
        self.assertEqual(response.data['code'], 1001)
        self.assertEqual(response.data['message'], "创建成功")
    
    def test_updated_success(self):
        """测试更新成功响应"""
        from .utils import ResponseHelper
        
        response = ResponseHelper.updated_success()
        
        self.assertEqual(response.data['code'], 1002)
        self.assertEqual(response.data['message'], "更新成功")
    
    def test_deleted_success(self):
        """测试删除成功响应"""
        from .utils import ResponseHelper
        
        response = ResponseHelper.deleted_success()
        
        self.assertEqual(response.data['code'], 1003)
        self.assertEqual(response.data['message'], "删除成功")
    
    def test_validation_error(self):
        """测试参数验证错误响应"""
        from .utils import ResponseHelper
        
        response = ResponseHelper.validation_error("用户名不能为空")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.VALIDATION_ERROR)
        self.assertEqual(response.data['message'], "用户名不能为空")
    
    def test_permission_denied(self):
        """测试权限拒绝响应"""
        from .utils import ResponseHelper
        
        response = ResponseHelper.permission_denied("无访问权限")
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['code'], ErrorCode.PERMISSION_DENIED)
        self.assertEqual(response.data['message'], "无访问权限")
    
    def test_not_found(self):
        """测试资源不存在响应"""
        from .utils import ResponseHelper
        
        response = ResponseHelper.not_found("用户不存在")
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['code'], ErrorCode.RESOURCE_NOT_FOUND)
        self.assertEqual(response.data['message'], "用户不存在")