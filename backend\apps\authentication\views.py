"""
认证模块 - 视图
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.viewsets import ModelViewSet
from rest_framework import status
from django.utils import timezone
from apps.common.response import ApiResponse
from apps.common.exceptions import BusinessException, ErrorCode
from apps.authentication.serializers import (
    CaptchaSerializer, LoginSerializer, TokenRefreshSerializer, 
    LogoutSerializer, UserSessionSerializer
)
from apps.authentication.services import (
    CaptchaService, AuthenticationService, SessionService
)
from apps.authentication.security import SecurityService
from apps.authentication.models import UserSession


@api_view(['GET'])
@permission_classes([AllowAny])
def captcha_view(request):
    """获取图形验证码"""
    try:
        captcha_data = CaptchaService.generate_captcha()
        serializer = CaptchaSerializer(captcha_data)
        return ApiResponse.success(
            data=serializer.data,
            message="验证码生成成功"
        )
    except Exception as e:
        return ApiResponse.error(
            message="验证码生成失败",
            code=ErrorCode.INTERNAL_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    """用户登录"""
    username = request.data.get('username', '')
    ip_address = SecurityService.get_client_ip(request)

    try:
        serializer = LoginSerializer(data=request.data)
        if not serializer.is_valid():
            # 记录失败的登录尝试
            SecurityService.record_login_attempt(
                username=username,
                ip_address=ip_address,
                request=request,
                is_successful=False,
                failure_reason="参数验证失败"
            )
            # 获取第一个错误信息
            error_message = "参数验证失败"
            if serializer.errors:
                first_error = next(iter(serializer.errors.values()))
                if isinstance(first_error, list) and first_error:
                    error_message = str(first_error[0])

            return ApiResponse.error(
                message=error_message,
                code=ErrorCode.LOGIN_FAILED
            )

        user = serializer.validated_data['user']

        # 应用安全策略
        security_results = SecurityService.apply_security_policies(user, request)

        # 记录成功的登录尝试
        SecurityService.record_login_attempt(
            username=username,
            ip_address=ip_address,
            request=request,
            is_successful=True
        )

        # 执行登录
        login_result = AuthenticationService.login(user, request)

        # 准备响应数据
        response_data = {
            'access': login_result['access'],
            'refresh': login_result['refresh'],
            'user': {
                'id': user.id,
                'username': user.username,
                'nickname': user.nickname,
                'email': user.email,
                'avatar': user.avatar,
                'last_login_time': user.last_login_time,
                'last_login_ip': user.last_login_ip,
            }
        }

        # 如果有安全警告，添加到响应中
        if security_results:
            response_data['security_warnings'] = security_results

        return ApiResponse.success(
            data=response_data,
            message="登录成功"
        )

    except BusinessException as e:
        # 记录失败的登录尝试
        SecurityService.record_login_attempt(
            username=username,
            ip_address=ip_address,
            request=request,
            is_successful=False,
            failure_reason=e.message
        )
        return ApiResponse.error(
            message=e.message,
            code=e.code
        )
    except Exception as e:
        # 记录失败的登录尝试
        SecurityService.record_login_attempt(
            username=username,
            ip_address=ip_address,
            request=request,
            is_successful=False,
            failure_reason="系统错误"
        )
        return ApiResponse.error(
            message="登录失败",
            code=ErrorCode.INTERNAL_ERROR
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_token_view(request):
    """刷新访问令牌"""
    try:
        serializer = TokenRefreshSerializer(data=request.data)
        if not serializer.is_valid():
            return ApiResponse.error(
                message="参数验证失败",
                code=ErrorCode.TOKEN_INVALID
            )
        
        refresh_token = serializer.validated_data['refresh']
        result = AuthenticationService.refresh_token(refresh_token)
        
        if result:
            return ApiResponse.success(
                data=result,
                message="令牌刷新成功"
            )
        else:
            return ApiResponse.error(
                message="令牌刷新失败",
                code=ErrorCode.TOKEN_INVALID
            )
    
    except Exception as e:
        return ApiResponse.error(
            message="令牌刷新失败",
            code=ErrorCode.INTERNAL_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """用户登出"""
    try:
        serializer = LogoutSerializer(data=request.data)
        serializer.is_valid()  # 不强制要求refresh token
        
        refresh_token = serializer.validated_data.get('refresh')
        
        # 从JWT令牌中获取会话ID
        session_id = None
        if hasattr(request.auth, 'payload'):
            session_id = request.auth.payload.get('session_id')
        
        # 执行登出
        AuthenticationService.logout(
            user=request.user,
            refresh_token=refresh_token,
            session_id=session_id
        )
        
        return ApiResponse.success(
            message="登出成功"
        )
    
    except Exception as e:
        return ApiResponse.error(
            message="登出失败",
            code=ErrorCode.INTERNAL_ERROR
        )


class UserSessionViewSet(ModelViewSet):
    """用户会话管理视图集"""
    serializer_class = UserSessionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的会话列表"""
        return UserSession.objects.filter(
            user=self.request.user
        ).order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        """获取用户会话列表"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return ApiResponse.success(
            data=serializer.data,
            message="获取会话列表成功"
        )
    
    def destroy(self, request, *args, **kwargs):
        """终止指定会话"""
        try:
            session = self.get_object()
            session.is_active = False
            session.save()
            
            return ApiResponse.success(
                message="会话已终止"
            )
        except Exception as e:
            return ApiResponse.error(
                message="终止会话失败",
                code=ErrorCode.INTERNAL_ERROR
            )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_all_view(request):
    """登出所有设备"""
    try:
        # 获取当前会话ID
        current_session_id = None
        if hasattr(request.auth, 'payload'):
            current_session_id = request.auth.payload.get('session_id')
        
        # 获取当前会话对象
        current_session = None
        if current_session_id:
            try:
                current_session = UserSession.objects.get(id=current_session_id)
            except UserSession.DoesNotExist:
                pass
        
        # 使所有其他会话失效
        SessionService.invalidate_user_sessions(
            user=request.user,
            exclude_session=current_session
        )
        
        return ApiResponse.success(
            message="已登出所有其他设备"
        )
    
    except Exception as e:
        return ApiResponse.error(
            message="操作失败",
            code=ErrorCode.INTERNAL_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def auth_status_view(request):
    """获取认证状态信息"""
    try:
        # 获取当前会话信息
        current_session = None
        if hasattr(request, 'session_obj'):
            current_session = request.session_obj

        # 获取用户所有活跃会话（优化查询）
        active_sessions = UserSession.objects.select_related('user').filter(
            user=request.user,
            is_active=True,
            expires_at__gt=timezone.now()
        ).order_by('-last_activity')

        # 序列化会话数据
        session_serializer = UserSessionSerializer(active_sessions, many=True)

        # 获取用户安全信息
        user = request.user
        security_info = {
            'login_fail_count': user.login_fail_count,
            'locked_until': user.locked_until,
            'last_login_time': user.last_login_time,
            'last_login_ip': user.last_login_ip,
            'is_locked': user.locked_until and user.locked_until > timezone.now(),
        }

        return ApiResponse.success(
            data={
                'current_session': {
                    'id': current_session.id if current_session else None,
                    'ip_address': current_session.ip_address if current_session else None,
                    'device_type': current_session.device_type if current_session else None,
                    'browser': current_session.browser if current_session else None,
                    'last_activity': current_session.last_activity if current_session else None,
                },
                'active_sessions': session_serializer.data,
                'security_info': security_info,
                'total_active_sessions': active_sessions.count(),
            },
            message="获取认证状态成功"
        )

    except Exception as e:
        return ApiResponse.error(
            message="获取认证状态失败",
            code=ErrorCode.INTERNAL_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def force_logout_session_view(request, session_id):
    """强制登出指定会话"""
    try:
        # 获取指定会话
        session = UserSession.objects.get(
            id=session_id,
            user=request.user,
            is_active=True
        )

        # 使会话失效
        session.is_active = False
        session.save()

        return ApiResponse.success(
            message=f"会话 {session_id} 已被强制登出"
        )

    except UserSession.DoesNotExist:
        return ApiResponse.error(
            message="会话不存在或已失效",
            code=ErrorCode.USER_NOT_FOUND
        )
    except Exception as e:
        return ApiResponse.error(
            message="强制登出失败",
            code=ErrorCode.INTERNAL_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def security_info_view(request):
    """获取安全信息"""
    try:
        from apps.authentication.models import LoginAttempt, IPWhitelist

        user = request.user

        # 获取最近的登录尝试记录（优化查询）
        recent_attempts = LoginAttempt.objects.filter(
            username=user.username
        ).select_related('user').order_by('-created_at')[:10]

        # 获取IP白名单（优化查询）
        ip_whitelist = IPWhitelist.objects.filter(
            user=user,
            is_active=True
        ).select_related('user')

        # 统计信息
        total_attempts = LoginAttempt.objects.filter(username=user.username).count()
        failed_attempts = LoginAttempt.objects.filter(
            username=user.username,
            is_successful=False
        ).count()

        # 最近24小时的登录尝试
        from django.utils import timezone
        last_24h = timezone.now() - timezone.timedelta(hours=24)
        recent_24h_attempts = LoginAttempt.objects.filter(
            username=user.username,
            created_at__gte=last_24h
        ).count()

        return ApiResponse.success(
            data={
                'recent_attempts': [
                    {
                        'id': attempt.id,
                        'ip_address': attempt.ip_address,
                        'is_successful': attempt.is_successful,
                        'failure_reason': attempt.failure_reason,
                        'device_fingerprint': attempt.device_fingerprint,
                        'created_at': attempt.created_at,
                    }
                    for attempt in recent_attempts
                ],
                'ip_whitelist': [
                    {
                        'id': ip.id,
                        'ip_address': ip.ip_address,
                        'description': ip.description,
                        'expires_at': ip.expires_at,
                        'created_at': ip.created_at,
                    }
                    for ip in ip_whitelist
                ],
                'statistics': {
                    'total_attempts': total_attempts,
                    'failed_attempts': failed_attempts,
                    'success_rate': round((total_attempts - failed_attempts) / total_attempts * 100, 2) if total_attempts > 0 else 0,
                    'recent_24h_attempts': recent_24h_attempts,
                },
                'security_settings': {
                    'login_fail_count': user.login_fail_count,
                    'locked_until': user.locked_until,
                    'is_locked': user.locked_until and user.locked_until > timezone.now(),
                }
            },
            message="获取安全信息成功"
        )

    except Exception as e:
        return ApiResponse.error(
            message="获取安全信息失败",
            code=ErrorCode.INTERNAL_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_ip_whitelist_view(request):
    """添加IP到白名单"""
    try:
        ip_address = request.data.get('ip_address')
        description = request.data.get('description', '')
        expires_days = request.data.get('expires_days')

        if not ip_address:
            return ApiResponse.error(
                message="IP地址不能为空",
                code=ErrorCode.INVALID_PARAMETER
            )

        # 计算过期时间
        expires_at = None
        if expires_days:
            expires_at = timezone.now() + timezone.timedelta(days=int(expires_days))

        # 添加到白名单
        whitelist_entry = SecurityService.add_ip_to_whitelist(
            user=request.user,
            ip_address=ip_address,
            description=description,
            expires_at=expires_at
        )

        return ApiResponse.success(
            data={
                'id': whitelist_entry.id,
                'ip_address': whitelist_entry.ip_address,
                'description': whitelist_entry.description,
                'expires_at': whitelist_entry.expires_at,
                'created_at': whitelist_entry.created_at,
            },
            message="IP白名单添加成功"
        )

    except Exception as e:
        return ApiResponse.error(
            message="添加IP白名单失败",
            code=ErrorCode.INTERNAL_ERROR
        )
