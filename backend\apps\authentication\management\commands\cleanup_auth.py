"""
清理认证相关过期数据的管理命令
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.authentication.models import SimpleCaptcha, UserSession
from apps.authentication.services import CaptchaService, SessionService


class Command(BaseCommand):
    help = '清理过期的验证码和会话数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--captcha',
            action='store_true',
            help='只清理过期验证码',
        )
        parser.add_argument(
            '--sessions',
            action='store_true',
            help='只清理过期会话',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='预览将要删除的数据，不实际执行删除',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        captcha_only = options['captcha']
        sessions_only = options['sessions']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('预览模式：将显示要删除的数据但不实际执行删除')
            )
        
        # 如果没有指定特定选项，则清理所有
        if not captcha_only and not sessions_only:
            captcha_only = sessions_only = True
        
        total_deleted = 0
        
        # 清理过期验证码
        if captcha_only:
            self.stdout.write('正在清理过期验证码...')
            
            if dry_run:
                expired_captcha_count = SimpleCaptcha.objects.filter(
                    expires_at__lt=timezone.now()
                ).count()
                self.stdout.write(f'  将删除 {expired_captcha_count} 个过期验证码')
                total_deleted += expired_captcha_count
            else:
                deleted_count, _ = CaptchaService.cleanup_expired_captcha()
                self.stdout.write(
                    self.style.SUCCESS(f'  已删除 {deleted_count} 个过期验证码')
                )
                total_deleted += deleted_count
        
        # 清理过期会话
        if sessions_only:
            self.stdout.write('正在清理过期会话...')
            
            if dry_run:
                expired_sessions_count = UserSession.objects.filter(
                    expires_at__lt=timezone.now()
                ).count()
                self.stdout.write(f'  将删除 {expired_sessions_count} 个过期会话')
                total_deleted += expired_sessions_count
            else:
                updated_count = SessionService.cleanup_expired_sessions()
                self.stdout.write(
                    self.style.SUCCESS(f'  已更新 {updated_count} 个过期会话状态')
                )
                total_deleted += updated_count
        
        # 显示总结
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'预览完成：共将处理 {total_deleted} 条记录')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'清理完成：共处理 {total_deleted} 条记录')
            )
        
        # 显示当前状态
        self.stdout.write('\n当前状态：')
        active_captcha_count = SimpleCaptcha.objects.filter(
            expires_at__gt=timezone.now()
        ).count()
        active_sessions_count = UserSession.objects.filter(
            is_active=True,
            expires_at__gt=timezone.now()
        ).count()
        
        self.stdout.write(f'  活跃验证码：{active_captcha_count} 个')
        self.stdout.write(f'  活跃会话：{active_sessions_count} 个')
