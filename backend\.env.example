# Django配置
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置
DB_ENGINE=django.db.backends.postgresql
DB_NAME=heim_auth
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Redis配置
REDIS_URL=redis://127.0.0.1:6379/1
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=HEIM系统 <<EMAIL>>
ADMIN_EMAIL=<EMAIL>

# JWT配置
JWT_ACCESS_TOKEN_LIFETIME_HOURS=2
JWT_REFRESH_TOKEN_LIFETIME_DAYS=7
JWT_ROTATE_REFRESH_TOKENS=True

# 会话配置
MAX_USER_SESSIONS=5

# 文件上传配置
MAX_UPLOAD_SIZE=********  # 10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# 安全配置
SECURE_SSL_REDIRECT=False
ENABLE_IP_WHITELIST=False
LOGIN_ATTEMPT_LIMIT=5
ACCOUNT_LOCK_DURATION_MINUTES=30