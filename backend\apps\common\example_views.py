# -*- coding: utf-8 -*-
"""
异常处理示例视图
用于演示统一异常处理机制的使用
"""
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from .exceptions import BusinessException, ErrorCode
from .response import ApiResponse


class ExceptionDemoView(APIView):
    """异常处理演示视图"""
    permission_classes = [AllowAny]  # 仅用于演示，实际项目中应该有适当的权限控制
    
    def get(self, request):
        """
        演示不同类型的异常处理
        
        查询参数:
        - error_type: 异常类型 (business|validation|integrity|system|none)
        """
        error_type = request.query_params.get('error_type', 'none')
        
        if error_type == 'business':
            # 演示业务异常
            raise BusinessException(
                code=ErrorCode.USER_NOT_FOUND,
                message="演示业务异常：用户不存在",
                data={"user_id": 123, "requested_at": "2024-01-01"}
            )
        
        elif error_type == 'validation':
            # 演示Django验证异常
            raise ValidationError({
                'username': ['用户名不能为空'],
                'email': ['请输入有效的邮箱地址', '该邮箱已被使用']
            })
        
        elif error_type == 'integrity':
            # 演示数据库完整性异常
            raise IntegrityError("UNIQUE constraint failed: users_userprofile.username")
        
        elif error_type == 'system':
            # 演示系统异常
            raise Exception("演示系统异常：数据库连接失败")
        
        elif error_type == 'permission':
            # 演示权限异常
            raise BusinessException(
                code=ErrorCode.PERMISSION_DENIED,
                message="您没有权限执行此操作",
                data={"required_permission": "user.view", "user_role": "guest"}
            )
        
        elif error_type == 'auth':
            # 演示认证异常
            raise BusinessException(
                code=ErrorCode.TOKEN_EXPIRED,
                message="登录令牌已过期，请重新登录"
            )
        
        # 正常响应
        return ApiResponse.success(
            data={
                "message": "异常处理演示接口正常工作",
                "available_error_types": [
                    "business", "validation", "integrity", 
                    "system", "permission", "auth"
                ],
                "usage": "添加查询参数 ?error_type=business 来测试不同类型的异常"
            },
            message="请求成功"
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def create_user_demo(request):
    """
    演示用户创建中的异常处理
    """
    username = request.data.get('username')
    email = request.data.get('email')
    
    # 模拟业务验证
    if not username:
        raise BusinessException(
            code=ErrorCode.INVALID_PARAMETER,
            message="用户名不能为空",
            data={"field": "username"}
        )
    
    if len(username) < 3:
        raise BusinessException(
            code=ErrorCode.VALIDATION_ERROR,
            message="用户名长度不能少于3个字符",
            data={"field": "username", "min_length": 3, "current_length": len(username)}
        )
    
    if username == 'admin':
        raise BusinessException(
            code=ErrorCode.USER_ALREADY_EXISTS,
            message="用户名已存在，请选择其他用户名",
            data={"suggested_usernames": ["admin1", "admin2", "admin_user"]}
        )
    
    if email and '@' not in email:
        raise BusinessException(
            code=ErrorCode.VALIDATION_ERROR,
            message="邮箱格式不正确",
            data={"field": "email", "format": "<EMAIL>"}
        )
    
    # 模拟成功创建
    return ApiResponse.success(
        data={
            "id": 123,
            "username": username,
            "email": email,
            "created_at": "2024-01-01T12:00:00Z"
        },
        message="用户创建成功"
    )


@api_view(['GET'])
@permission_classes([AllowAny])
def paginated_demo(request):
    """
    演示分页响应格式
    """
    # 模拟分页数据
    page = int(request.query_params.get('page', 1))
    page_size = int(request.query_params.get('page_size', 10))
    
    if page < 1:
        raise BusinessException(
            code=ErrorCode.INVALID_PARAMETER,
            message="页码必须大于0",
            data={"field": "page", "min_value": 1}
        )
    
    if page_size > 100:
        raise BusinessException(
            code=ErrorCode.INVALID_PARAMETER,
            message="每页数量不能超过100",
            data={"field": "page_size", "max_value": 100}
        )
    
    # 模拟数据
    total_count = 250
    total_pages = (total_count + page_size - 1) // page_size
    
    if page > total_pages:
        raise BusinessException(
            code=ErrorCode.RESOURCE_NOT_FOUND,
            message=f"页码超出范围，总共只有{total_pages}页",
            data={"total_pages": total_pages, "requested_page": page}
        )
    
    # 模拟当前页数据
    start_id = (page - 1) * page_size + 1
    data = [
        {"id": i, "name": f"用户{i}", "email": f"user{i}@example.com"}
        for i in range(start_id, min(start_id + page_size, total_count + 1))
    ]
    
    page_info = {
        "count": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_previous": page > 1
    }
    
    return ApiResponse.paginated_success(
        data=data,
        page_info=page_info,
        message="查询成功"
    )