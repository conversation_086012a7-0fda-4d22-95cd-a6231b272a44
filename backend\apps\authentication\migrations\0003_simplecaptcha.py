# Generated by Django 4.2.23 on 2025-07-30 02:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SimpleCaptcha',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('key', models.CharField(max_length=40, unique=True, verbose_name='验证码密钥')),
                ('question', models.Char<PERSON>ield(max_length=50, verbose_name='验证码问题')),
                ('answer', models.CharField(max_length=10, verbose_name='验证码答案')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
            ],
            options={
                'verbose_name': '验证码',
                'verbose_name_plural': '验证码',
                'db_table': 'auth_simple_captcha',
                'indexes': [models.Index(fields=['key'], name='auth_simple_key_006f59_idx'), models.Index(fields=['expires_at'], name='auth_simple_expires_c82e54_idx')],
            },
        ),
    ]
