"""
认证模块 - 安全服务
"""
import hashlib
import json
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from apps.authentication.models import IPWhitelist, LoginAttempt
from apps.authentication.tasks import send_security_alert
import logging

logger = logging.getLogger(__name__)


class SecurityService:
    """安全服务"""
    
    @staticmethod
    def check_ip_whitelist(user, ip_address):
        """检查IP是否在白名单中"""
        try:
            whitelist_entry = IPWhitelist.objects.get(
                user=user,
                ip_address=ip_address,
                is_active=True
            )
            return whitelist_entry.is_valid()
        except IPWhitelist.DoesNotExist:
            return False
    
    @staticmethod
    def add_ip_to_whitelist(user, ip_address, description="", expires_at=None):
        """添加IP到白名单"""
        whitelist_entry, created = IPWhitelist.objects.get_or_create(
            user=user,
            ip_address=ip_address,
            defaults={
                'description': description,
                'expires_at': expires_at,
                'is_active': True
            }
        )
        
        if not created:
            # 更新现有记录
            whitelist_entry.description = description
            whitelist_entry.expires_at = expires_at
            whitelist_entry.is_active = True
            whitelist_entry.save()
        
        return whitelist_entry
    
    @staticmethod
    def generate_device_fingerprint(request):
        """生成设备指纹"""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        accept_encoding = request.META.get('HTTP_ACCEPT_ENCODING', '')
        
        # 简单的设备指纹生成
        fingerprint_data = f"{user_agent}|{accept_language}|{accept_encoding}"
        return hashlib.md5(fingerprint_data.encode()).hexdigest()
    
    @staticmethod
    def generate_browser_fingerprint(request):
        """生成浏览器指纹（更详细）"""
        headers = {}
        for key, value in request.META.items():
            if key.startswith('HTTP_'):
                headers[key] = value
        
        # 排序并序列化头部信息
        sorted_headers = dict(sorted(headers.items()))
        return json.dumps(sorted_headers, sort_keys=True)
    
    @staticmethod
    def record_login_attempt(username, ip_address, request, is_successful, failure_reason=""):
        """记录登录尝试"""
        try:
            device_fingerprint = SecurityService.generate_device_fingerprint(request)
            browser_fingerprint = SecurityService.generate_browser_fingerprint(request)
            
            LoginAttempt.objects.create(
                username=username,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', '')[:500],
                is_successful=is_successful,
                failure_reason=failure_reason,
                device_fingerprint=device_fingerprint,
                browser_fingerprint=browser_fingerprint
            )
        except Exception as e:
            logger.error(f"记录登录尝试失败: {e}")
    
    @staticmethod
    def check_suspicious_activity(username, ip_address):
        """检查可疑活动"""
        # 检查过去1小时内的失败登录次数
        one_hour_ago = timezone.now() - timezone.timedelta(hours=1)
        
        # 同一IP的失败次数
        ip_failures = LoginAttempt.objects.filter(
            ip_address=ip_address,
            is_successful=False,
            created_at__gte=one_hour_ago
        ).count()
        
        # 同一用户名的失败次数
        username_failures = LoginAttempt.objects.filter(
            username=username,
            is_successful=False,
            created_at__gte=one_hour_ago
        ).count()
        
        # 判断是否可疑
        is_suspicious = ip_failures >= 10 or username_failures >= 5
        
        return {
            'is_suspicious': is_suspicious,
            'ip_failures': ip_failures,
            'username_failures': username_failures,
            'details': f"IP {ip_address} 失败 {ip_failures} 次，用户 {username} 失败 {username_failures} 次"
        }
    
    @staticmethod
    def check_device_change(user, request):
        """检查设备变更"""
        current_fingerprint = SecurityService.generate_device_fingerprint(request)
        
        # 获取用户最近的成功登录记录
        recent_success = LoginAttempt.objects.filter(
            username=user.username,
            is_successful=True
        ).order_by('-created_at').first()
        
        if recent_success and recent_success.device_fingerprint:
            if recent_success.device_fingerprint != current_fingerprint:
                return {
                    'device_changed': True,
                    'previous_fingerprint': recent_success.device_fingerprint,
                    'current_fingerprint': current_fingerprint,
                    'last_login_time': recent_success.created_at
                }
        
        return {'device_changed': False}
    
    @staticmethod
    def check_location_change(user, ip_address):
        """检查地理位置变更（简化版）"""
        # 这里可以集成IP地理位置服务
        # 目前只是简单检查IP是否变更
        if user.last_login_ip and user.last_login_ip != ip_address:
            return {
                'location_changed': True,
                'previous_ip': user.last_login_ip,
                'current_ip': ip_address
            }
        
        return {'location_changed': False}
    
    @staticmethod
    def apply_security_policies(user, request):
        """应用安全策略"""
        ip_address = SecurityService.get_client_ip(request)
        results = {}
        
        # 1. 检查IP白名单（如果启用）
        if getattr(settings, 'ENABLE_IP_WHITELIST', False):
            if not SecurityService.check_ip_whitelist(user, ip_address):
                results['ip_whitelist_failed'] = True
                return results
        
        # 2. 检查可疑活动
        suspicious_check = SecurityService.check_suspicious_activity(user.username, ip_address)
        if suspicious_check['is_suspicious']:
            results['suspicious_activity'] = suspicious_check
            # 发送安全警报（暂时禁用Celery）
            try:
                send_security_alert.delay(
                    user_id=user.id,
                    alert_type='suspicious_activity',
                    details=suspicious_check['details']
                )
            except Exception as e:
                logger.warning(f"发送安全警报失败: {e}")
        
        # 3. 检查设备变更
        device_check = SecurityService.check_device_change(user, request)
        if device_check['device_changed']:
            results['device_changed'] = device_check
            # 发送设备变更警报（暂时禁用Celery）
            try:
                send_security_alert.delay(
                    user_id=user.id,
                    alert_type='device_change',
                    details=f"检测到新设备登录"
                )
            except Exception as e:
                logger.warning(f"发送设备变更警报失败: {e}")
        
        # 4. 检查地理位置变更
        location_check = SecurityService.check_location_change(user, ip_address)
        if location_check['location_changed']:
            results['location_changed'] = location_check
            # 发送位置变更警报（暂时禁用Celery）
            try:
                send_security_alert.delay(
                    user_id=user.id,
                    alert_type='location_change',
                    details=f"检测到异地登录: {location_check['previous_ip']} -> {location_check['current_ip']}"
                )
            except Exception as e:
                logger.warning(f"发送位置变更警报失败: {e}")
        
        return results
    
    @staticmethod
    def get_client_ip(request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    @staticmethod
    def cleanup_old_login_attempts(days=30):
        """清理旧的登录尝试记录"""
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        deleted_count, _ = LoginAttempt.objects.filter(
            created_at__lt=cutoff_date
        ).delete()
        return deleted_count
