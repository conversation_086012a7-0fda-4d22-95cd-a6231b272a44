# Git工作流程指南

## 🌳 分支策略 (Git Flow)

### 主要分支
- **`master`** - 主分支，包含稳定的生产代码，只接受来自develop的合并
- **`develop`** - 开发分支，包含最新的开发功能，所有功能分支的基础

### 功能分支
- **`feature/task-{n}-{description}`** - 每个任务对应一个功能分支
- 例如：`feature/task-3-response-format`、`feature/task-4-jwt-auth`

### 其他分支类型
- **`hotfix/issue-description`** - 紧急修复分支，从master创建
- **`release/v{version}`** - 发布分支，准备发布时使用

## 🛠️ 自动化工具使用

### 开始新任务
```bash
# 使用任务管理工具开始新任务
python tools/task_manager.py start 3 "统一响应格式和错误处理机制"
```

### 完成任务
```bash
# 使用任务管理工具完成任务
python tools/task_manager.py complete 3
```

工具会自动：
- 创建和管理功能分支
- 执行标准的Git工作流程
- 生成规范的提交信息
- 合并分支并清理
- 创建里程碑标签
- 推送到远程仓库

## 🔄 手动操作流程（备用）

### 1. 开始新任务
```bash
git checkout develop
git pull origin develop
git checkout -b feature/task-3-response-format
```

### 2. 任务开发过程中
```bash
# 定期提交小的变更
git add .
git commit -m "feat: 实现ApiResponse统一响应格式类"
```

### 3. 任务完成后
```bash
# 最终提交
git add .
git commit -m "feat: 完成任务3 - 统一响应格式和错误处理机制"

# 合并到develop
git checkout develop
git pull origin develop
git merge feature/task-3-response-format --no-ff

# 清理分支
git branch -d feature/task-3-response-format
git push origin develop

# 创建标签
git tag -a v0.3.0-task3 -m "里程碑: 统一响应格式和错误处理机制完成"
git push origin v0.3.0-task3
```

## 提交信息规范

### 格式
```
<type>: <description>

[optional body]

[optional footer]
```

### 类型 (type)
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 重构代码
- `test`: 添加测试
- `chore`: 构建过程或辅助工具的变动

### 示例
```bash
feat: 完成任务3 - 统一响应格式和错误处理机制

- 实现ApiResponse统一API响应格式类
- 定义ErrorCode错误码常量
- 创建BusinessException自定义业务异常类
- 实现custom_exception_handler全局异常处理器
- 配置Django REST Framework使用自定义异常处理器
- 编写异常处理的单元测试用例

异常处理机制已完善，API响应格式统一
```

## 里程碑标签

每完成一个重要任务，创建标签：
```bash
git tag -a v0.1.0-task1 -m "完成任务1：项目环境搭建和基础配置"
git tag -a v0.1.0-task2 -m "完成任务2：数据模型设计和数据库迁移"
```

## 远程仓库配置

### 添加远程仓库
```bash
# 添加远程仓库（以GitHub为例）
git remote add origin https://gitee.com/ynzf/heim.git

# 推送主分支
git push -u origin master

# 推送开发分支
git push -u origin develop

# 推送所有标签
git push --tags
```

### 日常同步
```bash
# 推送当前分支
git push

# 拉取最新代码
git pull

# 推送标签
git push --tags
```

## 最佳实践

1. **小而频繁的提交** - 每个逻辑单元完成后就提交
2. **清晰的提交信息** - 描述做了什么和为什么
3. **任务完成检查** - 确保代码通过测试和检查
4. **分支清理** - 合并后删除功能分支
5. **定期同步** - 与远程仓库保持同步
6. **统一.gitignore** - 使用项目根目录的.gitignore，避免子目录重复配置

## 紧急修复流程

如果在任务执行过程中发现严重问题：
```bash
# 创建hotfix分支
git checkout master
git checkout -b hotfix/critical-fix

# 修复问题
git add .
git commit -m "fix: 修复关键安全漏洞"

# 合并到master和develop
git checkout master
git merge hotfix/critical-fix
git checkout develop
git merge hotfix/critical-fix
git branch -d hotfix/critical-fix
```