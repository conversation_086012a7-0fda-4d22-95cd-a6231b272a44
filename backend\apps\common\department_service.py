"""
用户部门关联管理服务
"""
from django.db import transaction
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import get_user_model
from apps.departments.models import Department, UserDepartment
from apps.common.permissions import DataScopeService
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class UserDepartmentService:
    """用户部门关联管理服务"""
    
    CACHE_PREFIX = 'user_dept'
    CACHE_TIMEOUT = 300  # 5分钟缓存
    
    @staticmethod
    def get_user_effective_departments(user, include_expired=False):
        """
        获取用户的有效部门关联
        
        Args:
            user: 用户对象
            include_expired: 是否包含过期的关联
        
        Returns:
            QuerySet: 用户部门关联查询集
        """
        cache_key = f"{UserDepartmentService.CACHE_PREFIX}:effective_depts:{user.id}:{include_expired}"
        relations = cache.get(cache_key)
        
        if relations is None:
            try:
                if include_expired:
                    relations = UserDepartment.objects.filter(
                        user=user, is_deleted=False
                    ).select_related('department').order_by('-is_primary', 'weight')
                else:
                    relations = UserDepartment.get_effective_relations(
                        user=user
                    ).select_related('department').order_by('-is_primary', 'weight')
                
                # 转换为列表以便缓存
                relations = list(relations)
                cache.set(cache_key, relations, UserDepartmentService.CACHE_TIMEOUT)
                
            except Exception as e:
                logger.error(f"获取用户有效部门关联失败: {e}")
                relations = []
        
        return relations
    
    @staticmethod
    def get_user_primary_department(user):
        """
        获取用户的主部门
        
        Args:
            user: 用户对象
        
        Returns:
            UserDepartment: 主部门关联对象，如果没有则返回None
        """
        cache_key = f"{UserDepartmentService.CACHE_PREFIX}:primary_dept:{user.id}"
        primary_dept = cache.get(cache_key)
        
        if primary_dept is None:
            try:
                primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                    is_primary=True
                ).select_related('department').first()
                
                cache.set(cache_key, primary_dept, UserDepartmentService.CACHE_TIMEOUT)
                
            except Exception as e:
                logger.error(f"获取用户主部门失败: {e}")
                primary_dept = None
        
        return primary_dept
    
    @staticmethod
    def get_user_managed_departments(user, level=None):
        """
        获取用户管理的部门
        
        Args:
            user: 用户对象
            level: 主管级别，None表示所有级别
        
        Returns:
            QuerySet: 用户管理的部门关联查询集
        """
        cache_key = f"{UserDepartmentService.CACHE_PREFIX}:managed_depts:{user.id}:{level}"
        relations = cache.get(cache_key)
        
        if relations is None:
            try:
                queryset = UserDepartment.get_effective_relations(user=user).filter(
                    is_manager=True
                ).select_related('department')
                
                if level:
                    queryset = queryset.filter(manager_level=level)
                
                relations = list(queryset.order_by('manager_level', 'weight'))
                cache.set(cache_key, relations, UserDepartmentService.CACHE_TIMEOUT)
                
            except Exception as e:
                logger.error(f"获取用户管理部门失败: {e}")
                relations = []
        
        return relations
    
    @staticmethod
    def assign_user_to_department(user, department, is_primary=False, is_manager=False, 
                                 position='', manager_level=None, weight=1, 
                                 effective_date=None, expiry_date=None):
        """
        分配用户到部门
        
        Args:
            user: 用户对象
            department: 部门对象
            is_primary: 是否为主部门
            is_manager: 是否为部门主管
            position: 职位
            manager_level: 主管级别
            weight: 权重
            effective_date: 生效日期
            expiry_date: 失效日期
        
        Returns:
            UserDepartment: 创建的用户部门关联对象
        """
        try:
            with transaction.atomic():
                # 检查是否已存在关联
                existing = UserDepartment.objects.filter(
                    user=user, department=department, is_deleted=False
                ).first()
                
                if existing:
                    # 更新现有关联
                    existing.is_primary = is_primary
                    existing.is_manager = is_manager
                    existing.position = position
                    existing.manager_level = manager_level if is_manager else None
                    existing.weight = weight
                    existing.effective_date = effective_date or timezone.now().date()
                    existing.expiry_date = expiry_date
                    existing.save()
                    
                    user_dept = existing
                else:
                    # 创建新关联
                    user_dept = UserDepartment.objects.create(
                        user=user,
                        department=department,
                        is_primary=is_primary,
                        is_manager=is_manager,
                        position=position,
                        manager_level=manager_level if is_manager else None,
                        weight=weight,
                        effective_date=effective_date or timezone.now().date(),
                        expiry_date=expiry_date
                    )
                
                # 如果设置为主部门，需要取消其他主部门
                if is_primary:
                    UserDepartment.objects.filter(
                        user=user, is_primary=True, is_deleted=False
                    ).exclude(id=user_dept.id).update(is_primary=False)
                
                # 清除相关缓存
                UserDepartmentService.clear_user_cache(user.id)
                DataScopeService.clear_user_cache(user.id)
                
                logger.info(f"用户 {user.username} 已分配到部门 {department.name}")
                return user_dept
                
        except Exception as e:
            logger.error(f"分配用户到部门失败: {e}")
            raise
    
    @staticmethod
    def remove_user_from_department(user, department):
        """
        从部门移除用户
        
        Args:
            user: 用户对象
            department: 部门对象
        
        Returns:
            bool: 是否成功移除
        """
        try:
            with transaction.atomic():
                user_dept = UserDepartment.objects.filter(
                    user=user, department=department, is_deleted=False
                ).first()
                
                if user_dept:
                    # 软删除
                    user_dept.soft_delete()
                    
                    # 如果删除的是主部门，需要重新设置主部门
                    if user_dept.is_primary:
                        UserDepartmentService._reassign_primary_department(user)
                    
                    # 清除相关缓存
                    UserDepartmentService.clear_user_cache(user.id)
                    DataScopeService.clear_user_cache(user.id)
                    
                    logger.info(f"用户 {user.username} 已从部门 {department.name} 移除")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"从部门移除用户失败: {e}")
            return False
    
    @staticmethod
    def _reassign_primary_department(user):
        """重新分配主部门"""
        try:
            # 找到权重最高的有效部门关联
            remaining_dept = UserDepartment.get_effective_relations(user=user).order_by(
                '-weight', 'created_at'
            ).first()
            
            if remaining_dept:
                remaining_dept.is_primary = True
                remaining_dept.save()
                logger.info(f"用户 {user.username} 的主部门已重新设置为 {remaining_dept.department.name}")
            
        except Exception as e:
            logger.error(f"重新分配主部门失败: {e}")
    
    @staticmethod
    def update_department_relation(user_dept_id, **kwargs):
        """
        更新用户部门关联
        
        Args:
            user_dept_id: 用户部门关联ID
            **kwargs: 要更新的字段
        
        Returns:
            UserDepartment: 更新后的关联对象
        """
        try:
            with transaction.atomic():
                user_dept = UserDepartment.objects.get(id=user_dept_id, is_deleted=False)
                
                # 更新字段
                for field, value in kwargs.items():
                    if hasattr(user_dept, field):
                        setattr(user_dept, field, value)
                
                user_dept.save()
                
                # 如果更新了主部门状态，需要处理其他主部门
                if 'is_primary' in kwargs and kwargs['is_primary']:
                    UserDepartment.objects.filter(
                        user=user_dept.user, is_primary=True, is_deleted=False
                    ).exclude(id=user_dept.id).update(is_primary=False)
                
                # 清除相关缓存
                UserDepartmentService.clear_user_cache(user_dept.user.id)
                DataScopeService.clear_user_cache(user_dept.user.id)
                
                return user_dept
                
        except UserDepartment.DoesNotExist:
            logger.error(f"用户部门关联 {user_dept_id} 不存在")
            raise
        except Exception as e:
            logger.error(f"更新用户部门关联失败: {e}")
            raise
    
    @staticmethod
    def check_department_relation_validity(user_dept):
        """
        检查用户部门关联的有效性
        
        Args:
            user_dept: 用户部门关联对象
        
        Returns:
            dict: 检查结果
        """
        result = {
            'is_valid': True,
            'issues': []
        }
        
        try:
            # 检查有效期
            if not user_dept.is_effective():
                result['is_valid'] = False
                result['issues'].append('关联已过期')
            
            # 检查部门是否激活
            if not user_dept.department.is_active:
                result['is_valid'] = False
                result['issues'].append('部门已禁用')
            
            # 检查用户是否激活
            if not user_dept.user.is_active:
                result['is_valid'] = False
                result['issues'].append('用户已禁用')
            
            # 检查主管级别设置
            if user_dept.is_manager and not user_dept.manager_level:
                result['issues'].append('主管未设置级别')
            
        except Exception as e:
            logger.error(f"检查用户部门关联有效性失败: {e}")
            result['is_valid'] = False
            result['issues'].append(f'检查异常: {str(e)}')
        
        return result
    
    @staticmethod
    def clear_user_cache(user_id):
        """清除用户相关缓存"""
        try:
            cache_keys = [
                f"{UserDepartmentService.CACHE_PREFIX}:effective_depts:{user_id}:True",
                f"{UserDepartmentService.CACHE_PREFIX}:effective_depts:{user_id}:False",
                f"{UserDepartmentService.CACHE_PREFIX}:primary_dept:{user_id}",
                f"{UserDepartmentService.CACHE_PREFIX}:managed_depts:{user_id}:None",
                f"{UserDepartmentService.CACHE_PREFIX}:managed_depts:{user_id}:1",
                f"{UserDepartmentService.CACHE_PREFIX}:managed_depts:{user_id}:2",
                f"{UserDepartmentService.CACHE_PREFIX}:managed_depts:{user_id}:3",
            ]
            
            cache.delete_many(cache_keys)
            logger.info(f"已清除用户 {user_id} 的部门关联缓存")
            
        except Exception as e:
            logger.error(f"清除用户部门关联缓存失败: {e}")
    
    @staticmethod
    def get_department_members(department, include_managers_only=False):
        """
        获取部门成员
        
        Args:
            department: 部门对象
            include_managers_only: 是否只包含主管
        
        Returns:
            QuerySet: 用户部门关联查询集
        """
        try:
            queryset = UserDepartment.get_effective_relations(department=department)
            
            if include_managers_only:
                queryset = queryset.filter(is_manager=True)
            
            return queryset.select_related('user').order_by('-is_manager', 'manager_level', 'weight')
            
        except Exception as e:
            logger.error(f"获取部门成员失败: {e}")
            return UserDepartment.objects.none()
