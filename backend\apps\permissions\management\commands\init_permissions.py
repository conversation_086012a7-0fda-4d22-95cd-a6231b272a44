"""
初始化权限数据的管理命令
"""
from django.core.management.base import BaseCommand
from apps.permissions.models import Permission, Role


class Command(BaseCommand):
    help = '初始化系统权限和角色数据'
    
    def handle(self, *args, **options):
        self.stdout.write('开始初始化权限数据...')
        
        # 创建基础权限
        self.create_permissions()
        
        # 创建基础角色
        self.create_roles()
        
        self.stdout.write(self.style.SUCCESS('权限数据初始化完成！'))
    
    def create_permissions(self):
        """创建基础权限"""
        permissions_data = [
            # 系统管理
            {
                'name': '系统管理',
                'code': 'system',
                'permission_type': 'MENU',
                'path': '/system',
                'component': 'Layout',
                'icon': 'setting',
                'sort_order': 1
            },
            
            # 用户管理
            {
                'name': '用户管理',
                'code': 'user:manage',
                'permission_type': 'MENU',
                'path': '/system/users',
                'component': 'UserManage',
                'icon': 'user',
                'sort_order': 10,
                'parent_code': 'system'
            },
            {
                'name': '用户查看',
                'code': 'user:view',
                'permission_type': 'API',
                'path': '/api/users/*',
                'http_method': 'GET',
                'sort_order': 11,
                'parent_code': 'user:manage'
            },
            {
                'name': '用户创建',
                'code': 'user:create',
                'permission_type': 'BUTTON',
                'sort_order': 12,
                'parent_code': 'user:manage'
            },
            {
                'name': '用户创建API',
                'code': 'user:create:api',
                'permission_type': 'API',
                'path': '/api/users/',
                'http_method': 'POST',
                'sort_order': 13,
                'parent_code': 'user:create'
            },
            {
                'name': '用户编辑',
                'code': 'user:edit',
                'permission_type': 'BUTTON',
                'sort_order': 14,
                'parent_code': 'user:manage'
            },
            {
                'name': '用户编辑API',
                'code': 'user:edit:api',
                'permission_type': 'API',
                'path': '/api/users/*',
                'http_method': 'PUT',
                'sort_order': 15,
                'parent_code': 'user:edit'
            },
            {
                'name': '用户删除',
                'code': 'user:delete',
                'permission_type': 'BUTTON',
                'sort_order': 16,
                'parent_code': 'user:manage'
            },
            {
                'name': '用户删除API',
                'code': 'user:delete:api',
                'permission_type': 'API',
                'path': '/api/users/*',
                'http_method': 'DELETE',
                'sort_order': 17,
                'parent_code': 'user:delete'
            },
            
            # 部门管理
            {
                'name': '部门管理',
                'code': 'dept:manage',
                'permission_type': 'MENU',
                'path': '/system/departments',
                'component': 'DeptManage',
                'icon': 'apartment',
                'sort_order': 20,
                'parent_code': 'system'
            },
            {
                'name': '部门查看',
                'code': 'dept:view',
                'permission_type': 'API',
                'path': '/api/departments/*',
                'http_method': 'GET',
                'sort_order': 21,
                'parent_code': 'dept:manage'
            },
            {
                'name': '部门创建',
                'code': 'dept:create',
                'permission_type': 'BUTTON',
                'sort_order': 22,
                'parent_code': 'dept:manage'
            },
            {
                'name': '部门编辑',
                'code': 'dept:edit',
                'permission_type': 'BUTTON',
                'sort_order': 24,
                'parent_code': 'dept:manage'
            },
            {
                'name': '部门删除',
                'code': 'dept:delete',
                'permission_type': 'BUTTON',
                'sort_order': 26,
                'parent_code': 'dept:manage'
            },
            
            # 角色权限管理
            {
                'name': '角色管理',
                'code': 'role:manage',
                'permission_type': 'MENU',
                'path': '/system/roles',
                'component': 'RoleManage',
                'icon': 'team',
                'sort_order': 30,
                'parent_code': 'system'
            },
            {
                'name': '角色查看',
                'code': 'role:view',
                'permission_type': 'API',
                'path': '/api/roles/*',
                'http_method': 'GET',
                'sort_order': 31,
                'parent_code': 'role:manage'
            },
            {
                'name': '角色创建',
                'code': 'role:create',
                'permission_type': 'BUTTON',
                'sort_order': 32,
                'parent_code': 'role:manage'
            },
            {
                'name': '角色编辑',
                'code': 'role:edit',
                'permission_type': 'BUTTON',
                'sort_order': 34,
                'parent_code': 'role:manage'
            },
            {
                'name': '角色删除',
                'code': 'role:delete',
                'permission_type': 'BUTTON',
                'sort_order': 36,
                'parent_code': 'role:manage'
            },
            {
                'name': '权限分配',
                'code': 'role:assign:permission',
                'permission_type': 'BUTTON',
                'sort_order': 37,
                'parent_code': 'role:manage'
            },
            {
                'name': '用户分配',
                'code': 'role:assign:user',
                'permission_type': 'BUTTON',
                'sort_order': 38,
                'parent_code': 'role:manage'
            },
            
            # 权限管理
            {
                'name': '权限管理',
                'code': 'permission:manage',
                'permission_type': 'MENU',
                'path': '/system/permissions',
                'component': 'PermissionManage',
                'icon': 'key',
                'sort_order': 40,
                'parent_code': 'system'
            },
            {
                'name': '权限查看',
                'code': 'permission:view',
                'permission_type': 'API',
                'path': '/api/permissions/*',
                'http_method': 'GET',
                'sort_order': 41,
                'parent_code': 'permission:manage'
            },
            {
                'name': '权限创建',
                'code': 'permission:create',
                'permission_type': 'BUTTON',
                'sort_order': 42,
                'parent_code': 'permission:manage'
            },
            {
                'name': '权限编辑',
                'code': 'permission:edit',
                'permission_type': 'BUTTON',
                'sort_order': 44,
                'parent_code': 'permission:manage'
            },
            {
                'name': '权限删除',
                'code': 'permission:delete',
                'permission_type': 'BUTTON',
                'sort_order': 46,
                'parent_code': 'permission:manage'
            },
        ]
        
        # 创建权限映射
        permission_map = {}
        
        # 第一遍：创建所有权限（不设置父权限）
        for perm_data in permissions_data:
            parent_code = perm_data.pop('parent_code', None)
            permission, created = Permission.objects.get_or_create(
                code=perm_data['code'],
                defaults=perm_data
            )
            permission_map[permission.code] = permission
            if created:
                self.stdout.write(f'创建权限: {permission.name}')
        
        # 第二遍：设置父权限关系
        for perm_data in permissions_data:
            parent_code = perm_data.get('parent_code')
            if parent_code and parent_code in permission_map:
                permission = permission_map[perm_data['code']]
                permission.parent = permission_map[parent_code]
                permission.save()
    
    def create_roles(self):
        """创建基础角色"""
        roles_data = [
            {
                'name': '超级管理员',
                'code': 'super_admin',
                'data_scope': 'ALL',
                'description': '系统超级管理员，拥有所有权限',
                'sort_order': 1
            },
            {
                'name': '系统管理员',
                'code': 'admin',
                'data_scope': 'ALL',
                'description': '系统管理员，拥有大部分管理权限',
                'sort_order': 2
            },
            {
                'name': '部门管理员',
                'code': 'dept_admin',
                'data_scope': 'DEPT_AND_SUB',
                'description': '部门管理员，可管理本部门及下级部门数据',
                'sort_order': 3
            },
            {
                'name': '普通用户',
                'code': 'user',
                'data_scope': 'SELF_ONLY',
                'description': '普通用户，只能查看和操作自己的数据',
                'sort_order': 4
            },
        ]
        
        for role_data in roles_data:
            role, created = Role.objects.get_or_create(
                code=role_data['code'],
                defaults=role_data
            )
            if created:
                self.stdout.write(f'创建角色: {role.name}')
                
                # 为超级管理员分配所有权限
                if role.code == 'super_admin':
                    all_permissions = Permission.objects.all()
                    role.permissions.set(all_permissions)
                    self.stdout.write(f'为{role.name}分配了所有权限')
                
                # 为系统管理员分配管理权限
                elif role.code == 'admin':
                    admin_permissions = Permission.objects.filter(
                        code__in=[
                            'system', 'user:manage', 'user:view', 'user:create', 'user:edit',
                            'dept:manage', 'dept:view', 'dept:create', 'dept:edit',
                            'role:manage', 'role:view', 'role:create', 'role:edit',
                            'permission:manage', 'permission:view'
                        ]
                    )
                    role.permissions.set(admin_permissions)
                    self.stdout.write(f'为{role.name}分配了管理权限')
                
                # 为部门管理员分配部门权限
                elif role.code == 'dept_admin':
                    dept_permissions = Permission.objects.filter(
                        code__in=[
                            'user:manage', 'user:view', 'user:create', 'user:edit',
                            'dept:manage', 'dept:view'
                        ]
                    )
                    role.permissions.set(dept_permissions)
                    self.stdout.write(f'为{role.name}分配了部门权限')
                
                # 为普通用户分配基础权限
                elif role.code == 'user':
                    user_permissions = Permission.objects.filter(
                        code__in=['user:view']
                    )
                    role.permissions.set(user_permissions)
                    self.stdout.write(f'为{role.name}分配了基础权限')
