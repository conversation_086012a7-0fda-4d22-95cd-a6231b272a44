"""
认证模块 - 会话管理模型
"""
from django.db import models
from django.utils import timezone
from apps.common.models import BaseModel
import random
import string


class UserSession(BaseModel):
    """用户会话管理 - 控制并发登录"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    session_key = models.CharField(max_length=40, unique=True, verbose_name="会话密钥")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(verbose_name="用户代理")
    
    # 会话状态
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    last_activity = models.DateTimeField(auto_now=True, verbose_name="最后活动时间")
    expires_at = models.DateTimeField(verbose_name="过期时间")
    
    # 设备信息
    device_type = models.CharField(max_length=50, blank=True, verbose_name="设备类型")
    browser = models.CharField(max_length=100, blank=True, verbose_name="浏览器")
    os = models.CharField(max_length=100, blank=True, verbose_name="操作系统")
    
    class Meta:
        db_table = 'auth_user_session'
        verbose_name = "用户会话"
        verbose_name_plural = "用户会话"
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.user.nickname} - {self.ip_address}"


class SimpleCaptcha(BaseModel):
    """简单数学验证码"""
    key = models.CharField(max_length=40, unique=True, verbose_name="验证码密钥")
    question = models.CharField(max_length=50, verbose_name="验证码问题")
    answer = models.CharField(max_length=10, verbose_name="验证码答案")
    expires_at = models.DateTimeField(verbose_name="过期时间")

    class Meta:
        db_table = 'auth_simple_captcha'
        verbose_name = "验证码"
        verbose_name_plural = "验证码"
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.question} = ?"

    @classmethod
    def generate_captcha(cls):
        """生成数学验证码"""
        # 生成简单的加法题
        num1 = random.randint(1, 20)
        num2 = random.randint(1, 20)
        question = f"{num1} + {num2}"
        answer = str(num1 + num2)

        # 生成唯一密钥
        key = ''.join(random.choices(string.ascii_lowercase + string.digits, k=40))

        # 设置5分钟过期
        expires_at = timezone.now() + timezone.timedelta(minutes=5)

        # 创建验证码记录
        captcha = cls.objects.create(
            key=key,
            question=question,
            answer=answer,
            expires_at=expires_at
        )

        return captcha

    @classmethod
    def verify_captcha(cls, key, answer):
        """验证验证码"""
        try:
            captcha = cls.objects.get(
                key=key,
                expires_at__gt=timezone.now()
            )
            if captcha.answer == str(answer).strip():
                # 验证成功后删除验证码
                captcha.delete()
                return True
            return False
        except cls.DoesNotExist:
            return False

    @classmethod
    def cleanup_expired(cls):
        """清理过期验证码"""
        return cls.objects.filter(expires_at__lt=timezone.now()).delete()


class IPWhitelist(BaseModel):
    """IP白名单"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    description = models.CharField(max_length=200, blank=True, verbose_name="描述")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name="过期时间")

    class Meta:
        db_table = 'auth_ip_whitelist'
        verbose_name = "IP白名单"
        verbose_name_plural = "IP白名单"
        unique_together = ['user', 'ip_address']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['ip_address']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.ip_address}"

    def is_valid(self):
        """检查IP白名单是否有效"""
        if not self.is_active:
            return False
        if self.expires_at and self.expires_at < timezone.now():
            return False
        return True


class LoginAttempt(BaseModel):
    """登录尝试记录"""
    username = models.CharField(max_length=150, verbose_name="用户名")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(verbose_name="用户代理")
    is_successful = models.BooleanField(verbose_name="是否成功")
    failure_reason = models.CharField(max_length=100, blank=True, verbose_name="失败原因")

    # 设备指纹信息
    device_fingerprint = models.CharField(max_length=64, blank=True, verbose_name="设备指纹")
    browser_fingerprint = models.TextField(blank=True, verbose_name="浏览器指纹")

    class Meta:
        db_table = 'auth_login_attempt'
        verbose_name = "登录尝试"
        verbose_name_plural = "登录尝试"
        indexes = [
            models.Index(fields=['username', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['is_successful', 'created_at']),
            models.Index(fields=['device_fingerprint']),
            models.Index(fields=['username', 'is_successful']),
            models.Index(fields=['ip_address', 'is_successful']),
        ]

    def __str__(self):
        status = "成功" if self.is_successful else "失败"
        return f"{self.username} - {self.ip_address} - {status}"