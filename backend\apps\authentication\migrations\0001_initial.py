# Generated by Django 4.2.23 on 2025-07-28 08:26

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('session_key', models.CharField(max_length=40, unique=True, verbose_name='会话密钥')),
                ('ip_address', models.GenericIPAddress<PERSON>ield(verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='最后活动时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('device_type', models.CharField(blank=True, max_length=50, verbose_name='设备类型')),
                ('browser', models.CharField(blank=True, max_length=100, verbose_name='浏览器')),
                ('os', models.CharField(blank=True, max_length=100, verbose_name='操作系统')),
            ],
            options={
                'verbose_name': '用户会话',
                'verbose_name_plural': '用户会话',
                'db_table': 'auth_user_session',
            },
        ),
    ]
