"""
测试数据范围权限系统的管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from apps.departments.models import Department, UserDepartment
from apps.permissions.models import Role, UserRole
from apps.common.permissions import DataScopeService
from apps.common.department_service import UserDepartmentService

User = get_user_model()


class Command(BaseCommand):
    help = '测试数据范围权限系统'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='创建测试数据',
        )
        parser.add_argument(
            '--test-permissions',
            action='store_true',
            help='测试权限功能',
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='清理测试数据',
        )
    
    def handle(self, *args, **options):
        if options['create_test_data']:
            self.create_test_data()
        
        if options['test_permissions']:
            self.test_permissions()
        
        if options['cleanup']:
            self.cleanup_test_data()
    
    def create_test_data(self):
        """创建测试数据"""
        self.stdout.write('开始创建测试数据...')
        
        try:
            with transaction.atomic():
                # 创建用户
                self.ceo = User.objects.get_or_create(
                    username='test_ceo',
                    defaults={
                        'password': 'testpass123',
                        'nickname': '测试CEO',
                        'email': '<EMAIL>'
                    }
                )[0]
                
                self.tech_manager = User.objects.get_or_create(
                    username='test_tech_manager',
                    defaults={
                        'password': 'testpass123',
                        'nickname': '测试技术总监',
                        'email': '<EMAIL>'
                    }
                )[0]
                
                self.developer = User.objects.get_or_create(
                    username='test_developer',
                    defaults={
                        'password': 'testpass123',
                        'nickname': '测试开发工程师',
                        'email': '<EMAIL>'
                    }
                )[0]
                
                # 创建部门结构
                self.company = Department.objects.get_or_create(
                    code='test_company',
                    defaults={'name': '测试公司'}
                )[0]
                
                self.tech_dept = Department.objects.get_or_create(
                    code='test_tech',
                    defaults={
                        'name': '测试技术部',
                        'parent': self.company
                    }
                )[0]
                
                self.dev_team = Department.objects.get_or_create(
                    code='test_dev',
                    defaults={
                        'name': '测试开发组',
                        'parent': self.tech_dept
                    }
                )[0]
                
                # 创建角色
                self.ceo_role = Role.objects.get_or_create(
                    code='test_ceo_role',
                    defaults={
                        'name': '测试CEO角色',
                        'data_scope': 'ALL'
                    }
                )[0]
                
                self.manager_role = Role.objects.get_or_create(
                    code='test_manager_role',
                    defaults={
                        'name': '测试管理员角色',
                        'data_scope': 'DEPT_AND_SUB'
                    }
                )[0]
                
                self.employee_role = Role.objects.get_or_create(
                    code='test_employee_role',
                    defaults={
                        'name': '测试员工角色',
                        'data_scope': 'SELF_ONLY'
                    }
                )[0]
                
                # 分配用户到部门
                UserDepartmentService.assign_user_to_department(
                    self.ceo, self.company, is_primary=True, is_manager=True
                )
                UserDepartmentService.assign_user_to_department(
                    self.tech_manager, self.tech_dept, is_primary=True, is_manager=True
                )
                UserDepartmentService.assign_user_to_department(
                    self.developer, self.dev_team, is_primary=True
                )
                
                # 分配角色
                UserRole.objects.get_or_create(
                    user=self.ceo, role=self.ceo_role
                )
                UserRole.objects.get_or_create(
                    user=self.tech_manager, role=self.manager_role
                )
                UserRole.objects.get_or_create(
                    user=self.developer, role=self.employee_role
                )
                
                self.stdout.write(self.style.SUCCESS('测试数据创建成功！'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'创建测试数据失败: {e}'))
    
    def test_permissions(self):
        """测试权限功能"""
        self.stdout.write('开始测试权限功能...')
        
        try:
            # 获取测试用户
            ceo = User.objects.get(username='test_ceo')
            tech_manager = User.objects.get(username='test_tech_manager')
            developer = User.objects.get(username='test_developer')
            
            # 测试数据范围获取
            self.stdout.write('\n=== 测试数据范围获取 ===')
            ceo_scope = DataScopeService.get_user_data_scope(ceo)
            tech_scope = DataScopeService.get_user_data_scope(tech_manager)
            dev_scope = DataScopeService.get_user_data_scope(developer)
            
            self.stdout.write(f'CEO数据范围: {ceo_scope}')
            self.stdout.write(f'技术总监数据范围: {tech_scope}')
            self.stdout.write(f'开发工程师数据范围: {dev_scope}')
            
            # 测试可访问部门
            self.stdout.write('\n=== 测试可访问部门 ===')
            ceo_depts = DataScopeService.get_user_accessible_departments(ceo)
            tech_depts = DataScopeService.get_user_accessible_departments(tech_manager)
            dev_depts = DataScopeService.get_user_accessible_departments(developer)
            
            self.stdout.write(f'CEO可访问部门: {[d.name for d in ceo_depts]}')
            self.stdout.write(f'技术总监可访问部门: {[d.name for d in tech_depts]}')
            self.stdout.write(f'开发工程师可访问部门: {[d.name for d in dev_depts]}')
            
            # 测试数据过滤
            self.stdout.write('\n=== 测试数据过滤 ===')
            all_users = User.objects.all()
            
            ceo_filtered = DataScopeService.apply_data_scope_to_queryset(
                all_users, ceo, 'department'
            )
            tech_filtered = DataScopeService.apply_data_scope_to_queryset(
                all_users, tech_manager, 'department'
            )
            dev_filtered = DataScopeService.apply_data_scope_to_queryset(
                all_users, developer, 'department'
            )
            
            self.stdout.write(f'CEO可访问用户数: {ceo_filtered.count()}')
            self.stdout.write(f'技术总监可访问用户数: {tech_filtered.count()}')
            self.stdout.write(f'开发工程师可访问用户数: {dev_filtered.count()}')
            
            # 测试对象访问权限
            self.stdout.write('\n=== 测试对象访问权限 ===')
            test_user = User.objects.create_user(
                username='test_object_user',
                password='testpass123',
                nickname='测试对象用户'
            )
            test_user.created_by = developer
            test_user.save()
            
            ceo_access = DataScopeService.check_data_access_permission(ceo, test_user)
            tech_access = DataScopeService.check_data_access_permission(tech_manager, test_user)
            dev_access = DataScopeService.check_data_access_permission(developer, test_user)
            
            self.stdout.write(f'CEO访问权限: {ceo_access}')
            self.stdout.write(f'技术总监访问权限: {tech_access}')
            self.stdout.write(f'开发工程师访问权限: {dev_access}')
            
            # 测试自定义数据范围
            self.stdout.write('\n=== 测试自定义数据范围 ===')
            custom_rules = {
                'department_ids': [Department.objects.get(code='test_tech').id]
            }
            custom_filter = DataScopeService.get_data_scope_filter(
                developer, 'CUSTOM', custom_rules=custom_rules
            )
            self.stdout.write(f'自定义过滤器: {custom_filter}')
            
            # 测试缓存功能
            self.stdout.write('\n=== 测试缓存功能 ===')
            # 第一次调用
            start_time = timezone.now()
            roles1 = DataScopeService.get_user_roles_with_data_scope(tech_manager)
            first_call_time = timezone.now() - start_time
            
            # 第二次调用（应该从缓存获取）
            start_time = timezone.now()
            roles2 = DataScopeService.get_user_roles_with_data_scope(tech_manager)
            second_call_time = timezone.now() - start_time
            
            self.stdout.write(f'第一次调用时间: {first_call_time}')
            self.stdout.write(f'第二次调用时间: {second_call_time}')
            self.stdout.write(f'缓存生效: {second_call_time < first_call_time}')
            
            self.stdout.write(self.style.SUCCESS('\n权限功能测试完成！'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'权限功能测试失败: {e}'))
            import traceback
            self.stdout.write(traceback.format_exc())
    
    def cleanup_test_data(self):
        """清理测试数据"""
        self.stdout.write('开始清理测试数据...')
        
        try:
            # 删除测试用户
            User.objects.filter(username__startswith='test_').delete()
            
            # 删除测试部门
            Department.objects.filter(code__startswith='test_').delete()
            
            # 删除测试角色
            Role.objects.filter(code__startswith='test_').delete()
            
            self.stdout.write(self.style.SUCCESS('测试数据清理完成！'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'清理测试数据失败: {e}'))
    
    def display_test_results(self):
        """显示测试结果"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('数据范围权限系统测试结果')
        self.stdout.write('='*50)
        
        # 显示系统状态
        user_count = User.objects.count()
        dept_count = Department.objects.count()
        role_count = Role.objects.count()
        
        self.stdout.write(f'用户总数: {user_count}')
        self.stdout.write(f'部门总数: {dept_count}')
        self.stdout.write(f'角色总数: {role_count}')
        
        self.stdout.write('\n测试建议:')
        self.stdout.write('1. 运行单元测试: python manage.py test apps.common.tests.test_data_scope')
        self.stdout.write('2. 检查缓存配置: 确保Redis正常运行')
        self.stdout.write('3. 监控性能: 观察数据范围过滤的查询性能')
        self.stdout.write('4. 验证权限: 在实际业务场景中测试权限控制')
