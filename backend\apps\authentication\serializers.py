"""
认证模块 - 序列化器
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.utils import timezone
from apps.users.models import UserProfile
from apps.authentication.models import SimpleCaptcha
from apps.common.exceptions import BusinessException, ErrorCode


class CaptchaSerializer(serializers.Serializer):
    """验证码序列化器"""
    captcha_key = serializers.CharField(read_only=True)
    captcha_question = serializers.CharField(read_only=True)


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    username = serializers.Char<PERSON>ield(max_length=150, help_text="用户名")
    password = serializers.CharField(max_length=128, help_text="密码")
    captcha = serializers.CharField(max_length=10, help_text="验证码")
    captcha_key = serializers.CharField(max_length=40, help_text="验证码密钥")
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        captcha = attrs.get('captcha')
        captcha_key = attrs.get('captcha_key')
        
        # 验证码验证
        if not self._validate_captcha(captcha, captcha_key):
            raise BusinessException(ErrorCode.CAPTCHA_ERROR, "验证码错误")
        
        # 获取用户
        try:
            user = UserProfile.objects.get(username=username)
        except UserProfile.DoesNotExist:
            raise BusinessException(ErrorCode.LOGIN_FAILED, "用户名或密码错误")
        
        # 检查账户状态
        if not user.is_active:
            raise BusinessException(ErrorCode.ACCOUNT_DISABLED, "账户已被禁用")
        
        # 检查账户锁定状态
        if user.locked_until and user.locked_until > timezone.now():
            remaining_time = int((user.locked_until - timezone.now()).total_seconds() / 60)
            raise BusinessException(
                ErrorCode.ACCOUNT_LOCKED, 
                f"账户已被锁定，请{remaining_time}分钟后重试"
            )
        
        # 验证密码
        if not user.check_password(password):
            # 增加失败次数
            user.login_fail_count += 1
            
            # 检查是否需要锁定账户
            max_attempts = 5  # 最大尝试次数
            if user.login_fail_count >= max_attempts:
                # 锁定30分钟
                user.locked_until = timezone.now() + timezone.timedelta(minutes=30)
                user.save()
                raise BusinessException(
                    ErrorCode.ACCOUNT_LOCKED, 
                    f"登录失败次数过多，账户已被锁定30分钟"
                )
            else:
                user.save()
                remaining_attempts = max_attempts - user.login_fail_count
                raise BusinessException(
                    ErrorCode.LOGIN_FAILED, 
                    f"用户名或密码错误，还有{remaining_attempts}次尝试机会"
                )
        
        # 登录成功，重置失败次数
        user.login_fail_count = 0
        user.locked_until = None
        user.save()
        
        attrs['user'] = user
        return attrs
    
    def _validate_captcha(self, captcha, captcha_key):
        """验证数学验证码"""
        return SimpleCaptcha.verify_captcha(captcha_key, captcha)


class TokenRefreshSerializer(serializers.Serializer):
    """令牌刷新序列化器"""
    refresh = serializers.CharField(help_text="刷新令牌")


class LogoutSerializer(serializers.Serializer):
    """登出序列化器"""
    refresh = serializers.CharField(help_text="刷新令牌", required=False)


class UserSessionSerializer(serializers.ModelSerializer):
    """用户会话序列化器"""
    user_nickname = serializers.CharField(source='user.nickname', read_only=True)
    
    class Meta:
        from apps.authentication.models import UserSession
        model = UserSession
        fields = [
            'id', 'user_nickname', 'ip_address', 'device_type', 
            'browser', 'os', 'is_active', 'last_activity', 
            'expires_at', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
