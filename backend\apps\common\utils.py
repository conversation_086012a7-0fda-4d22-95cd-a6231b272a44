# -*- coding: utf-8 -*-
"""
公共工具函数模块
"""
from .exceptions import BusinessException, ErrorCode
from .response import ApiResponse


def raise_business_error(code, message, data=None):
    """
    抛出业务异常的便捷函数
    
    Args:
        code: 错误码
        message: 错误消息
        data: 附加数据
        
    Raises:
        BusinessException: 业务异常
    """
    raise BusinessException(code=code, message=message, data=data)


def validate_required_fields(data, required_fields):
    """
    验证必填字段
    
    Args:
        data: 要验证的数据字典
        required_fields: 必填字段列表
        
    Raises:
        BusinessException: 当有必填字段缺失时
    """
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == '':
            missing_fields.append(field)
    
    if missing_fields:
        raise_business_error(
            code=ErrorCode.INVALID_PARAMETER,
            message=f"缺少必填字段: {', '.join(missing_fields)}"
        )


def validate_user_exists(user):
    """
    验证用户是否存在
    
    Args:
        user: 用户对象或None
        
    Raises:
        BusinessException: 当用户不存在时
    """
    if not user:
        raise_business_error(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在"
        )


def validate_user_active(user):
    """
    验证用户是否激活
    
    Args:
        user: 用户对象
        
    Raises:
        BusinessException: 当用户未激活时
    """
    if not user.is_active:
        raise_business_error(
            code=ErrorCode.ACCOUNT_DISABLED,
            message="用户账户已被禁用"
        )


def validate_permission(user, permission_code):
    """
    验证用户权限（占位函数，实际实现在权限模块）
    
    Args:
        user: 用户对象
        permission_code: 权限编码
        
    Raises:
        BusinessException: 当用户无权限时
    """
    # 这里是占位实现，实际权限验证逻辑在权限模块中
    # 这个函数主要是为了演示如何使用业务异常
    pass


class ResponseHelper:
    """响应助手类，提供常用的响应方法"""
    
    @staticmethod
    def success_with_data(data, message="操作成功"):
        """返回带数据的成功响应"""
        return ApiResponse.success(data=data, message=message)
    
    @staticmethod
    def success_without_data(message="操作成功"):
        """返回不带数据的成功响应"""
        return ApiResponse.success(message=message)
    
    @staticmethod
    def created_success(data, message="创建成功"):
        """返回创建成功响应"""
        return ApiResponse.success(data=data, message=message, code=1001)
    
    @staticmethod
    def updated_success(data=None, message="更新成功"):
        """返回更新成功响应"""
        return ApiResponse.success(data=data, message=message, code=1002)
    
    @staticmethod
    def deleted_success(message="删除成功"):
        """返回删除成功响应"""
        return ApiResponse.success(message=message, code=1003)
    
    @staticmethod
    def validation_error(message="参数验证失败"):
        """返回参数验证错误响应"""
        return ApiResponse.error(
            message=message,
            code=ErrorCode.VALIDATION_ERROR
        )
    
    @staticmethod
    def permission_denied(message="权限不足"):
        """返回权限拒绝响应"""
        return ApiResponse.error(
            message=message,
            code=ErrorCode.PERMISSION_DENIED,
            status_code=403
        )
    
    @staticmethod
    def not_found(message="资源不存在"):
        """返回资源不存在响应"""
        return ApiResponse.error(
            message=message,
            code=ErrorCode.RESOURCE_NOT_FOUND,
            status_code=404
        )