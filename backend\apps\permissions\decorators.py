"""
权限管理模块 - 权限验证装饰器
"""
from functools import wraps
from django.http import JsonResponse
from django.utils import timezone
from rest_framework import status
from apps.permissions.services import PermissionService
from apps.common.exceptions import ErrorCode


def require_permission(permission_code):
    """
    权限验证装饰器
    
    Args:
        permission_code (str): 权限编码
    
    Usage:
        @require_permission('user:create')
        def create_user(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否已认证
            if not request.user.is_authenticated:
                return JsonResponse({
                    'code': ErrorCode.TOKEN_INVALID,
                    'message': '用户未认证',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # 超级管理员跳过权限检查
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # 检查用户权限
            if not PermissionService.check_user_permission(request.user, permission_code):
                return JsonResponse({
                    'code': ErrorCode.PERMISSION_DENIED,
                    'message': f'权限不足，需要权限: {permission_code}',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_403_FORBIDDEN)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_permissions(*permission_codes, require_all=True):
    """
    多权限验证装饰器
    
    Args:
        permission_codes: 权限编码列表
        require_all (bool): 是否需要所有权限，False表示只需要其中一个
    
    Usage:
        @require_permissions('user:create', 'user:update', require_all=False)
        def manage_user(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否已认证
            if not request.user.is_authenticated:
                return JsonResponse({
                    'code': ErrorCode.TOKEN_INVALID,
                    'message': '用户未认证',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # 超级管理员跳过权限检查
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # 检查权限
            user_permissions = [
                PermissionService.check_user_permission(request.user, code)
                for code in permission_codes
            ]
            
            if require_all:
                # 需要所有权限
                if not all(user_permissions):
                    missing_permissions = [
                        code for code, has_perm in zip(permission_codes, user_permissions)
                        if not has_perm
                    ]
                    return JsonResponse({
                        'code': ErrorCode.PERMISSION_DENIED,
                        'message': f'权限不足，缺少权限: {", ".join(missing_permissions)}',
                        'data': None,
                        'timestamp': timezone.now().isoformat()
                    }, status=status.HTTP_403_FORBIDDEN)
            else:
                # 只需要其中一个权限
                if not any(user_permissions):
                    return JsonResponse({
                        'code': ErrorCode.PERMISSION_DENIED,
                        'message': f'权限不足，需要以下权限之一: {", ".join(permission_codes)}',
                        'data': None,
                        'timestamp': timezone.now().isoformat()
                    }, status=status.HTTP_403_FORBIDDEN)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_data_scope(data_scope_level):
    """
    数据范围权限验证装饰器
    
    Args:
        data_scope_level (str): 所需的数据范围级别
    
    Usage:
        @require_data_scope('ALL')
        def get_all_users(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否已认证
            if not request.user.is_authenticated:
                return JsonResponse({
                    'code': ErrorCode.TOKEN_INVALID,
                    'message': '用户未认证',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # 超级管理员跳过权限检查
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # 检查数据范围权限
            user_data_scope = PermissionService.get_user_data_scope(request.user)
            
            # 数据范围权限级别
            scope_levels = {
                'SELF_ONLY': 1,
                'DEPT_ONLY': 2,
                'DEPT_AND_SUB': 3,
                'ALL': 4,
                'CUSTOM': 0
            }
            
            user_level = scope_levels.get(user_data_scope, 0)
            required_level = scope_levels.get(data_scope_level, 4)
            
            if user_level < required_level:
                return JsonResponse({
                    'code': ErrorCode.PERMISSION_DENIED,
                    'message': f'数据范围权限不足，当前权限: {user_data_scope}，需要权限: {data_scope_level}',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_403_FORBIDDEN)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_role(role_code):
    """
    角色验证装饰器
    
    Args:
        role_code (str): 角色编码
    
    Usage:
        @require_role('admin')
        def admin_only_view(request):
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否已认证
            if not request.user.is_authenticated:
                return JsonResponse({
                    'code': ErrorCode.TOKEN_INVALID,
                    'message': '用户未认证',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # 超级管理员跳过权限检查
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # 检查用户角色
            user_roles = PermissionService.get_user_roles(request.user)
            role_codes = [role.code for role in user_roles]
            
            if role_code not in role_codes:
                return JsonResponse({
                    'code': ErrorCode.PERMISSION_DENIED,
                    'message': f'角色权限不足，需要角色: {role_code}',
                    'data': None,
                    'timestamp': timezone.now().isoformat()
                }, status=status.HTTP_403_FORBIDDEN)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


class PermissionMixin:
    """权限验证混入类（用于类视图）"""
    
    required_permissions = []  # 必需的权限列表
    required_role = None  # 必需的角色
    required_data_scope = None  # 必需的数据范围
    require_all_permissions = True  # 是否需要所有权限
    
    def check_permissions(self, request):
        """检查权限"""
        # 检查用户是否已认证
        if not request.user.is_authenticated:
            return False, '用户未认证'
        
        # 超级管理员跳过权限检查
        if request.user.is_superuser:
            return True, ''
        
        # 检查角色
        if self.required_role:
            user_roles = PermissionService.get_user_roles(request.user)
            role_codes = [role.code for role in user_roles]
            if self.required_role not in role_codes:
                return False, f'角色权限不足，需要角色: {self.required_role}'
        
        # 检查权限
        if self.required_permissions:
            user_permissions = [
                PermissionService.check_user_permission(request.user, code)
                for code in self.required_permissions
            ]
            
            if self.require_all_permissions:
                if not all(user_permissions):
                    missing_permissions = [
                        code for code, has_perm in zip(self.required_permissions, user_permissions)
                        if not has_perm
                    ]
                    return False, f'权限不足，缺少权限: {", ".join(missing_permissions)}'
            else:
                if not any(user_permissions):
                    return False, f'权限不足，需要以下权限之一: {", ".join(self.required_permissions)}'
        
        # 检查数据范围
        if self.required_data_scope:
            user_data_scope = PermissionService.get_user_data_scope(request.user)
            scope_levels = {
                'SELF_ONLY': 1,
                'DEPT_ONLY': 2,
                'DEPT_AND_SUB': 3,
                'ALL': 4,
                'CUSTOM': 0
            }
            
            user_level = scope_levels.get(user_data_scope, 0)
            required_level = scope_levels.get(self.required_data_scope, 4)
            
            if user_level < required_level:
                return False, f'数据范围权限不足，当前权限: {user_data_scope}，需要权限: {self.required_data_scope}'
        
        return True, ''
    
    def dispatch(self, request, *args, **kwargs):
        """重写dispatch方法，添加权限检查"""
        has_permission, error_message = self.check_permissions(request)
        
        if not has_permission:
            status_code = status.HTTP_401_UNAUTHORIZED if '未认证' in error_message else status.HTTP_403_FORBIDDEN
            error_code = ErrorCode.TOKEN_INVALID if '未认证' in error_message else ErrorCode.PERMISSION_DENIED
            
            return JsonResponse({
                'code': error_code,
                'message': error_message,
                'data': None,
                'timestamp': timezone.now().isoformat()
            }, status=status_code)
        
        return super().dispatch(request, *args, **kwargs)
