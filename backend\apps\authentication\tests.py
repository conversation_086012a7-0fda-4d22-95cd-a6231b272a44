"""
认证模块 - 测试
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from apps.authentication.models import UserSession, SimpleCaptcha
from apps.authentication.services import CaptchaService, SessionService, AuthenticationService

User = get_user_model()


class CaptchaServiceTestCase(TestCase):
    """验证码服务测试"""
    
    def test_generate_captcha(self):
        """测试生成验证码"""
        captcha_data = CaptchaService.generate_captcha()

        self.assertIn('captcha_key', captcha_data)
        self.assertIn('captcha_question', captcha_data)
        self.assertTrue(SimpleCaptcha.objects.filter(key=captcha_data['captcha_key']).exists())
    
    def test_cleanup_expired_captcha(self):
        """测试清理过期验证码"""
        # 创建验证码
        captcha_data = CaptchaService.generate_captcha()
        
        # 验证验证码存在
        self.assertTrue(SimpleCaptcha.objects.filter(key=captcha_data['captcha_key']).exists())
        
        # 清理过期验证码（这里只是测试方法调用）
        result = CaptchaService.cleanup_expired_captcha()
        self.assertIsNotNone(result)


class AuthenticationAPITestCase(TestCase):
    """认证API测试"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户',
            email='<EMAIL>'
        )
    
    def test_captcha_generation(self):
        """测试验证码生成接口"""
        url = reverse('authentication:captcha')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertIn('captcha_key', response.data['data'])
        self.assertIn('captcha_question', response.data['data'])
    
    def test_login_without_captcha(self):
        """测试没有验证码的登录"""
        url = reverse('authentication:login')
        data = {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': '1234',
            'captcha_key': 'invalid_key'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], 2002)  # CAPTCHA_ERROR
    
    def test_login_with_invalid_credentials(self):
        """测试错误凭据登录"""
        # 先生成验证码
        captcha_data = CaptchaService.generate_captcha()
        captcha = SimpleCaptcha.objects.get(key=captcha_data['captcha_key'])

        url = reverse('authentication:login')
        data = {
            'username': 'testuser',
            'password': 'wrongpassword',
            'captcha': captcha.answer,
            'captcha_key': captcha_data['captcha_key']
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], 2001)  # LOGIN_FAILED

    def test_successful_login(self):
        """测试成功登录"""
        # 先生成验证码
        captcha_data = CaptchaService.generate_captcha()
        captcha = SimpleCaptcha.objects.get(key=captcha_data['captcha_key'])

        url = reverse('authentication:login')
        data = {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': captcha.answer,
            'captcha_key': captcha_data['captcha_key']
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)
        self.assertIn('access', response.data['data'])
        self.assertIn('refresh', response.data['data'])
        self.assertIn('user', response.data['data'])
    
    def test_logout(self):
        """测试登出"""
        from django.test import RequestFactory

        # 创建模拟请求
        factory = RequestFactory()
        request = factory.get('/')
        request.META['HTTP_USER_AGENT'] = 'Test Browser'
        request.META['REMOTE_ADDR'] = '127.0.0.1'

        # 先登录
        login_result = AuthenticationService.login(self.user, request)

        # 设置认证头
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {login_result["access"]}')

        url = reverse('authentication:logout')
        data = {
            'refresh': login_result['refresh']
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1000)


class SessionServiceTestCase(TestCase):
    """会话服务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
    
    def test_create_session(self):
        """测试创建会话"""
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.get('/')
        request.META['HTTP_USER_AGENT'] = 'Test Browser'
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        session = SessionService.create_session(self.user, request)
        
        self.assertIsInstance(session, UserSession)
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.ip_address, '127.0.0.1')
        self.assertTrue(session.is_active)
    
    def test_cleanup_expired_sessions(self):
        """测试清理过期会话"""
        result = SessionService.cleanup_expired_sessions()
        self.assertIsNotNone(result)


class AccountLockTestCase(TestCase):
    """账户锁定测试"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
    
    def test_account_lock_after_failed_attempts(self):
        """测试多次失败登录后账户锁定"""
        # 模拟5次失败登录
        for i in range(5):
            captcha_data = CaptchaService.generate_captcha()
            captcha = SimpleCaptcha.objects.get(key=captcha_data['captcha_key'])

            url = reverse('authentication:login')
            data = {
                'username': 'testuser',
                'password': 'wrongpassword',
                'captcha': captcha.answer,
                'captcha_key': captcha_data['captcha_key']
            }
            response = self.client.post(url, data)
            
            if i < 4:  # 前4次应该返回登录失败
                self.assertEqual(response.data['code'], 2001)
            else:  # 第5次应该返回账户锁定
                self.assertEqual(response.data['code'], 2005)
        
        # 刷新用户对象
        self.user.refresh_from_db()
        self.assertIsNotNone(self.user.locked_until)
        self.assertEqual(self.user.login_fail_count, 5)
