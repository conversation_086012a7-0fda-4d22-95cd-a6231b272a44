<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          HEIM企业管理平台
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          用户认证与权限管理系统
        </p>
      </div>
      
      <n-form
        ref="formRef"
        :model="loginForm"
        :rules="rules"
        size="large"
        class="mt-8 space-y-6"
      >
        <n-form-item path="username" label="用户名">
          <n-input
            v-model:value="loginForm.username"
            placeholder="请输入用户名"
            :disabled="loading"
          />
        </n-form-item>
        
        <n-form-item path="password" label="密码">
          <n-input
            v-model:value="loginForm.password"
            type="password"
            placeholder="请输入密码"
            :disabled="loading"
            @keyup.enter="handleLogin"
          />
        </n-form-item>
        
        <n-form-item path="captcha" label="验证码">
          <div class="flex space-x-2">
            <n-input
              v-model:value="loginForm.captcha"
              placeholder="请输入验证码答案"
              :disabled="loading"
              @keyup.enter="handleLogin"
              class="flex-1"
            />
            <n-button
              @click="refreshCaptcha"
              :disabled="loading"
              class="whitespace-nowrap"
            >
              {{ captchaQuestion || '获取验证码' }}
            </n-button>
          </div>
        </n-form-item>
        
        <n-form-item>
          <n-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="w-full"
          >
            登录
          </n-button>
        </n-form-item>
      </n-form>
      
      <div v-if="errorMessage" class="text-red-600 text-center">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'

const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  captcha: '',
  captcha_key: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

// 状态
const loading = ref(false)
const errorMessage = ref('')
const captchaQuestion = ref('')
const formRef = ref()

// 获取验证码
const refreshCaptcha = async () => {
  try {
    const response = await authApi.getCaptcha()
    if (response.code === 1000) {
      captchaQuestion.value = response.data.captcha_question
      loginForm.value.captcha_key = response.data.captcha_key
      loginForm.value.captcha = ''
    }
  } catch (error) {
    message.error('获取验证码失败')
  }
}

// 登录处理
const handleLogin = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    errorMessage.value = ''
    
    const loginData = await authStore.login(loginForm.value)
    
    message.success('登录成功')
    router.push('/')
    
  } catch (error: any) {
    if (error.response?.data?.message) {
      errorMessage.value = error.response.data.message
    } else if (error.message) {
      errorMessage.value = error.message
    } else {
      errorMessage.value = '登录失败，请重试'
    }
    
    // 刷新验证码
    await refreshCaptcha()
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取验证码
onMounted(() => {
  refreshCaptcha()
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
