import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authApi, type LoginForm, type LoginResponse } from '@/api/auth'

// 用户信息类型
export interface UserInfo {
  id: number
  username: string
  nickname: string
  email: string
  avatar: string
  last_login_time: string
  last_login_ip: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('access_token') || '')
  const refreshToken = ref<string>(localStorage.getItem('refresh_token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  const currentDepartment = ref<number | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })

  // 方法
  const login = async (form: LoginForm): Promise<LoginResponse> => {
    try {
      const response = await authApi.login(form)
      
      if (response.code === 1000) {
        // 保存令牌
        token.value = response.data.access
        refreshToken.value = response.data.refresh
        userInfo.value = response.data.user
        
        // 持久化存储
        localStorage.setItem('access_token', response.data.access)
        localStorage.setItem('refresh_token', response.data.refresh)
        localStorage.setItem('user_info', JSON.stringify(response.data.user))
        
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      if (refreshToken.value) {
        await authApi.logout({ refresh: refreshToken.value })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除状态
      token.value = ''
      refreshToken.value = ''
      userInfo.value = null
      permissions.value = []
      currentDepartment.value = null
      
      // 清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')
    }
  }

  const refreshTokenAction = async (): Promise<void> => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await authApi.refreshToken({ refresh: refreshToken.value })
      
      if (response.code === 1000) {
        token.value = response.data.access
        refreshToken.value = response.data.refresh
        
        localStorage.setItem('access_token', response.data.access)
        localStorage.setItem('refresh_token', response.data.refresh)
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
      await logout()
      throw error
    }
  }

  const refreshUserInfo = async () => {
    try {
      const response = await authApi.getAuthStatus()
      if (response.code === 1000) {
        // 更新用户信息（如果需要的话）
        // userInfo.value = response.data.user_info
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
    }
  }

  // 初始化时恢复用户信息
  const initializeAuth = () => {
    const savedUserInfo = localStorage.getItem('user_info')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('user_info')
      }
    }
  }

  return {
    token,
    refreshToken,
    userInfo,
    permissions,
    currentDepartment,
    isLoggedIn,
    hasPermission,
    login,
    logout,
    refreshToken: refreshTokenAction,
    refreshUserInfo,
    initializeAuth
  }
})
