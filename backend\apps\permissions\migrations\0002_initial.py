# Generated by Django 4.2.23 on 2025-07-28 08:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('permissions', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userrole',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='role',
            name='permissions',
            field=models.ManyToManyField(blank=True, to='permissions.permission', verbose_name='权限'),
        ),
        migrations.AddField(
            model_name='role',
            name='users',
            field=models.ManyToManyField(through='permissions.UserRole', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='permission',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='permissions.permission', verbose_name='父权限'),
        ),
        migrations.AlterUniqueTogether(
            name='userrole',
            unique_together={('user', 'role', 'department')},
        ),
        migrations.AddIndex(
            model_name='permission',
            index=models.Index(fields=['permission_type', 'is_active'], name='sys_permiss_permiss_185a66_idx'),
        ),
        migrations.AddIndex(
            model_name='permission',
            index=models.Index(fields=['parent', 'sort_order'], name='sys_permiss_parent__c63ced_idx'),
        ),
    ]
