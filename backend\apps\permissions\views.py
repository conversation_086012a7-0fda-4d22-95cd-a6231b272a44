"""
权限管理模块 - 视图集
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db import transaction
from django.db.models import Q, Count
from django.contrib.auth import get_user_model

from apps.common.response import ApiResponse
from apps.common.exceptions import BusinessException, ErrorCode
from apps.permissions.models import Role, Permission, UserRole
from apps.permissions.serializers import (
    RoleSerializer, PermissionSerializer, PermissionTreeSerializer,
    UserRoleSerializer, RoleAssignmentSerializer, UserPermissionSerializer
)

User = get_user_model()


class PermissionViewSet(viewsets.ModelViewSet):
    """权限管理视图集"""
    
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = Permission.objects.filter(is_deleted=False)
        
        # 权限类型过滤
        permission_type = self.request.query_params.get('permission_type')
        if permission_type:
            queryset = queryset.filter(permission_type=permission_type)
        
        # 状态过滤
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # 父权限过滤
        parent_id = self.request.query_params.get('parent_id')
        if parent_id:
            if parent_id == '0':  # 根权限
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)
        
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(code__icontains=search) |
                Q(path__icontains=search)
            )
        
        return queryset.order_by('sort_order', 'name')
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'tree':
            return PermissionTreeSerializer
        return PermissionSerializer
    
    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取权限树形结构"""
        try:
            # 获取根权限
            root_permissions = Permission.objects.filter(
                parent__isnull=True, 
                is_deleted=False, 
                is_active=True
            ).order_by('sort_order', 'name')
            
            serializer = self.get_serializer(root_permissions, many=True)
            return ApiResponse.success(serializer.data, "获取权限树成功")
        except Exception as e:
            return ApiResponse.error(f"获取权限树失败: {str(e)}")
    
    @action(detail=False, methods=['get'])
    def types(self, request):
        """获取权限类型列表"""
        try:
            types = [{'value': choice[0], 'label': choice[1]} 
                    for choice in Permission.PERMISSION_TYPE_CHOICES]
            return ApiResponse.success(types, "获取权限类型成功")
        except Exception as e:
            return ApiResponse.error(f"获取权限类型失败: {str(e)}")
    
    def create(self, request, *args, **kwargs):
        """创建权限"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            permission = serializer.save()
            return ApiResponse.success(
                PermissionSerializer(permission).data, 
                "权限创建成功"
            )
        except Exception as e:
            return ApiResponse.error(f"权限创建失败: {str(e)}")
    
    def update(self, request, *args, **kwargs):
        """更新权限"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            permission = serializer.save()
            return ApiResponse.success(
                PermissionSerializer(permission).data, 
                "权限更新成功"
            )
        except Exception as e:
            return ApiResponse.error(f"权限更新失败: {str(e)}")
    
    def destroy(self, request, *args, **kwargs):
        """删除权限（软删除）"""
        try:
            instance = self.get_object()
            
            # 检查是否有子权限
            if instance.children.filter(is_deleted=False).exists():
                return ApiResponse.error("该权限下存在子权限，无法删除", ErrorCode.PERMISSION_HAS_CHILDREN)
            
            # 检查是否被角色使用
            if instance.role_set.filter(is_deleted=False).exists():
                return ApiResponse.error("该权限正在被角色使用，无法删除", ErrorCode.PERMISSION_IN_USE)
            
            # 软删除
            instance.soft_delete()
            return ApiResponse.success(None, "权限删除成功")
        except Exception as e:
            return ApiResponse.error(f"权限删除失败: {str(e)}")


class RoleViewSet(viewsets.ModelViewSet):
    """角色管理视图集"""
    
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = Role.objects.filter(is_deleted=False)
        
        # 状态过滤
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # 数据范围过滤
        data_scope = self.request.query_params.get('data_scope')
        if data_scope:
            queryset = queryset.filter(data_scope=data_scope)
        
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(code__icontains=search) |
                Q(description__icontains=search)
            )
        
        return queryset.order_by('sort_order', 'name')

    def create(self, request, *args, **kwargs):
        """创建角色"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            role = serializer.save()
            return ApiResponse.success(
                RoleSerializer(role).data,
                "角色创建成功"
            )
        except Exception as e:
            return ApiResponse.error(f"角色创建失败: {str(e)}")

    def update(self, request, *args, **kwargs):
        """更新角色"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            role = serializer.save()
            return ApiResponse.success(
                RoleSerializer(role).data,
                "角色更新成功"
            )
        except Exception as e:
            return ApiResponse.error(f"角色更新失败: {str(e)}")

    @action(detail=False, methods=['get'])
    def data_scopes(self, request):
        """获取数据范围选项"""
        try:
            scopes = [{'value': choice[0], 'label': choice[1]} 
                     for choice in Role.DATA_SCOPE_CHOICES]
            return ApiResponse.success(scopes, "获取数据范围选项成功")
        except Exception as e:
            return ApiResponse.error(f"获取数据范围选项失败: {str(e)}")
    
    @action(detail=True, methods=['get'])
    def permissions(self, request, pk=None):
        """获取角色权限"""
        try:
            role = self.get_object()
            permissions = role.permissions.filter(is_deleted=False, is_active=True)
            serializer = PermissionSerializer(permissions, many=True)
            return ApiResponse.success(serializer.data, "获取角色权限成功")
        except Exception as e:
            return ApiResponse.error(f"获取角色权限失败: {str(e)}")
    
    @action(detail=True, methods=['post'])
    def assign_permissions(self, request, pk=None):
        """分配权限给角色"""
        try:
            role = self.get_object()
            permission_ids = request.data.get('permission_ids', [])
            
            # 验证权限是否存在
            permissions = Permission.objects.filter(
                id__in=permission_ids, 
                is_deleted=False, 
                is_active=True
            )
            
            if len(permissions) != len(permission_ids):
                return ApiResponse.error("部分权限不存在或已禁用")
            
            # 分配权限
            role.permissions.set(permissions)
            
            return ApiResponse.success(None, "权限分配成功")
        except Exception as e:
            return ApiResponse.error(f"权限分配失败: {str(e)}")
    
    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """获取角色用户"""
        try:
            role = self.get_object()
            user_roles = UserRole.objects.filter(
                role=role, 
                is_deleted=False
            ).select_related('user', 'department')
            
            serializer = UserRoleSerializer(user_roles, many=True)
            return ApiResponse.success(serializer.data, "获取角色用户成功")
        except Exception as e:
            return ApiResponse.error(f"获取角色用户失败: {str(e)}")
    
    @action(detail=True, methods=['post'])
    def assign_users(self, request, pk=None):
        """分配用户给角色"""
        try:
            role = self.get_object()
            serializer = RoleAssignmentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            user_ids = serializer.validated_data['user_ids']
            department_id = serializer.validated_data.get('department_id')
            
            with transaction.atomic():
                # 批量创建用户角色关联
                user_roles = []
                for user_id in user_ids:
                    # 检查是否已存在
                    if not UserRole.objects.filter(
                        user_id=user_id, 
                        role=role, 
                        department_id=department_id,
                        is_deleted=False
                    ).exists():
                        user_roles.append(UserRole(
                            user_id=user_id,
                            role=role,
                            department_id=department_id
                        ))
                
                UserRole.objects.bulk_create(user_roles)
            
            return ApiResponse.success(None, f"成功分配{len(user_roles)}个用户")
        except Exception as e:
            return ApiResponse.error(f"用户分配失败: {str(e)}")
    
    def destroy(self, request, *args, **kwargs):
        """删除角色（软删除）"""
        try:
            instance = self.get_object()
            
            # 检查是否有用户使用该角色
            if UserRole.objects.filter(role=instance, is_deleted=False).exists():
                return ApiResponse.error("该角色正在被用户使用，无法删除", ErrorCode.ROLE_IN_USE)
            
            # 软删除
            instance.soft_delete()
            return ApiResponse.success(None, "角色删除成功")
        except Exception as e:
            return ApiResponse.error(f"角色删除失败: {str(e)}")


class UserRoleViewSet(viewsets.ModelViewSet):
    """用户角色关联管理视图集"""
    
    queryset = UserRole.objects.all()
    serializer_class = UserRoleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = UserRole.objects.filter(is_deleted=False).select_related(
            'user', 'role', 'department'
        )
        
        # 用户过滤
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # 角色过滤
        role_id = self.request.query_params.get('role_id')
        if role_id:
            queryset = queryset.filter(role_id=role_id)
        
        # 部门过滤
        department_id = self.request.query_params.get('department_id')
        if department_id:
            queryset = queryset.filter(department_id=department_id)
        
        return queryset.order_by('-created_at')
    
    @action(detail=False, methods=['post'])
    def batch_delete(self, request):
        """批量删除用户角色关联"""
        try:
            ids = request.data.get('ids', [])
            if not ids:
                return ApiResponse.error("请选择要删除的记录")
            
            # 软删除
            UserRole.objects.filter(id__in=ids).update(is_deleted=True)
            
            return ApiResponse.success(None, f"成功删除{len(ids)}条记录")
        except Exception as e:
            return ApiResponse.error(f"批量删除失败: {str(e)}")
    
    @action(detail=False, methods=['get'])
    def user_permissions(self, request):
        """获取用户的所有权限"""
        try:
            user_id = request.query_params.get('user_id')
            if not user_id:
                return ApiResponse.error("请提供用户ID")

            try:
                user = User.objects.get(id=user_id, is_deleted=False)
            except User.DoesNotExist:
                return ApiResponse.error("用户不存在")

            # 获取用户权限信息
            from apps.permissions.services import PermissionService

            permissions = PermissionService.get_user_permissions(user)
            roles = PermissionService.get_user_roles(user)
            data_scope = PermissionService.get_user_data_scope(user)
            menu_permissions = PermissionService.get_user_menu_permissions(user)
            button_permissions = PermissionService.get_user_button_permissions(user)
            api_permissions = PermissionService.get_user_api_permissions(user)

            result = {
                'user_id': user.id,
                'username': user.username,
                'nickname': user.nickname,
                'data_scope': data_scope,
                'permissions': [
                    {
                        'id': p.id,
                        'name': p.name,
                        'code': p.code,
                        'permission_type': p.permission_type,
                        'path': p.path,
                        'component': p.component,
                        'icon': p.icon
                    } for p in permissions
                ],
                'roles': [
                    {
                        'id': r.id,
                        'name': r.name,
                        'code': r.code,
                        'data_scope': r.data_scope
                    } for r in roles
                ],
                'menu_permissions': menu_permissions,
                'button_permissions': button_permissions,
                'api_permissions': api_permissions
            }

            return ApiResponse.success(result, "获取用户权限成功")
        except Exception as e:
            return ApiResponse.error(f"获取用户权限失败: {str(e)}")
