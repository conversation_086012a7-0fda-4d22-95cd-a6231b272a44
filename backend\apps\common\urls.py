# -*- coding: utf-8 -*-
"""
公共模块URL配置
"""
from django.urls import path
from .test_views import TestExceptionView, test_response_helper
from .example_views import ExceptionDemoView, create_user_demo, paginated_demo

app_name = 'common'

urlpatterns = [
    # 测试异常处理
    path('test/exception/', TestExceptionView.as_view(), name='test_exception'),
    # 测试响应助手
    path('test/response/', test_response_helper, name='test_response'),
    
    # 异常处理演示
    path('demo/exception/', ExceptionDemoView.as_view(), name='exception_demo'),
    path('demo/create-user/', create_user_demo, name='create_user_demo'),
    path('demo/paginated/', paginated_demo, name='paginated_demo'),
]