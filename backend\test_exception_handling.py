#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
异常处理功能测试脚本
用于验证统一异常处理机制是否正常工作
"""
import os
import sys
import django
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from apps.common.exceptions import BusinessException, ErrorCode
from apps.common.response import ApiResponse

User = get_user_model()


def test_api_response_format():
    """测试API响应格式"""
    print("=" * 50)
    print("测试API响应格式")
    print("=" * 50)
    
    # 测试成功响应
    print("\n1. 测试成功响应:")
    response = ApiResponse.success(
        data={"id": 1, "name": "测试用户"},
        message="查询成功"
    )
    print(f"状态码: {response.status_code}")
    print(f"响应数据: {json.dumps(response.data, ensure_ascii=False, indent=2)}")
    
    # 测试错误响应
    print("\n2. 测试错误响应:")
    response = ApiResponse.error(
        message="用户不存在",
        code=ErrorCode.USER_NOT_FOUND
    )
    print(f"状态码: {response.status_code}")
    print(f"响应数据: {json.dumps(response.data, ensure_ascii=False, indent=2)}")
    
    # 测试分页响应
    print("\n3. 测试分页响应:")
    page_info = {
        "count": 100,
        "page": 1,
        "page_size": 10,
        "total_pages": 10
    }
    response = ApiResponse.paginated_success(
        data=[{"id": 1}, {"id": 2}],
        page_info=page_info
    )
    print(f"状态码: {response.status_code}")
    print(f"响应数据: {json.dumps(response.data, ensure_ascii=False, indent=2)}")


def test_business_exception():
    """测试业务异常"""
    print("\n" + "=" * 50)
    print("测试业务异常")
    print("=" * 50)
    
    try:
        raise BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="用户不存在",
            data={"user_id": 123}
        )
    except BusinessException as e:
        print(f"异常代码: {e.code}")
        print(f"异常消息: {e.message}")
        print(f"异常数据: {e.data}")
        print(f"异常字符串: {str(e)}")


def test_error_codes():
    """测试错误码定义"""
    print("\n" + "=" * 50)
    print("测试错误码定义")
    print("=" * 50)
    
    print("认证相关错误码:")
    print(f"  LOGIN_FAILED: {ErrorCode.LOGIN_FAILED}")
    print(f"  TOKEN_EXPIRED: {ErrorCode.TOKEN_EXPIRED}")
    print(f"  ACCOUNT_LOCKED: {ErrorCode.ACCOUNT_LOCKED}")
    
    print("\n权限相关错误码:")
    print(f"  PERMISSION_DENIED: {ErrorCode.PERMISSION_DENIED}")
    print(f"  INSUFFICIENT_PERMISSIONS: {ErrorCode.INSUFFICIENT_PERMISSIONS}")
    
    print("\n业务相关错误码:")
    print(f"  USER_NOT_FOUND: {ErrorCode.USER_NOT_FOUND}")
    print(f"  USER_ALREADY_EXISTS: {ErrorCode.USER_ALREADY_EXISTS}")
    print(f"  DEPARTMENT_HAS_CHILDREN: {ErrorCode.DEPARTMENT_HAS_CHILDREN}")
    
    print("\n系统相关错误码:")
    print(f"  INTERNAL_ERROR: {ErrorCode.INTERNAL_ERROR}")
    print(f"  DATABASE_ERROR: {ErrorCode.DATABASE_ERROR}")
    print(f"  NETWORK_ERROR: {ErrorCode.NETWORK_ERROR}")


def test_demo_endpoints():
    """测试演示端点"""
    print("\n" + "=" * 50)
    print("测试演示端点")
    print("=" * 50)
    
    client = Client()
    
    # 测试正常响应
    print("\n1. 测试正常响应:")
    response = client.get('/api/common/demo/exception/')
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    # 测试业务异常
    print("\n2. 测试业务异常:")
    response = client.get('/api/common/demo/exception/?error_type=business')
    print(f"状态码: {response.status_code}")
    if response.status_code == 400:
        data = response.json()
        print(f"错误响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    # 测试验证异常
    print("\n3. 测试验证异常:")
    response = client.get('/api/common/demo/exception/?error_type=validation')
    print(f"状态码: {response.status_code}")
    if response.status_code == 400:
        data = response.json()
        print(f"错误响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    # 测试用户创建演示
    print("\n4. 测试用户创建演示 - 参数错误:")
    response = client.post('/api/common/demo/create-user/', 
                          data=json.dumps({"username": ""}),
                          content_type='application/json')
    print(f"状态码: {response.status_code}")
    if response.status_code == 400:
        data = response.json()
        print(f"错误响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    # 测试用户创建演示 - 成功
    print("\n5. 测试用户创建演示 - 成功:")
    response = client.post('/api/common/demo/create-user/', 
                          data=json.dumps({"username": "testuser", "email": "<EMAIL>"}),
                          content_type='application/json')
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"成功响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    # 测试分页演示
    print("\n6. 测试分页演示:")
    response = client.get('/api/common/demo/paginated/?page=1&page_size=5')
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"分页响应: {json.dumps(data, ensure_ascii=False, indent=2)}")


def main():
    """主函数"""
    print("HEIM 统一异常处理机制测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 测试API响应格式
        test_api_response_format()
        
        # 测试业务异常
        test_business_exception()
        
        # 测试错误码
        test_error_codes()
        
        # 测试演示端点
        test_demo_endpoints()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()