# Generated by Django 4.2.23 on 2025-07-30 03:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('authentication', '0003_simplecaptcha'),
    ]

    operations = [
        migrations.CreateModel(
            name='LoginAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('username', models.CharField(max_length=150, verbose_name='用户名')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('is_successful', models.BooleanField(verbose_name='是否成功')),
                ('failure_reason', models.CharField(blank=True, max_length=100, verbose_name='失败原因')),
                ('device_fingerprint', models.CharField(blank=True, max_length=64, verbose_name='设备指纹')),
                ('browser_fingerprint', models.TextField(blank=True, verbose_name='浏览器指纹')),
            ],
            options={
                'verbose_name': '登录尝试',
                'verbose_name_plural': '登录尝试',
                'db_table': 'auth_login_attempt',
                'indexes': [models.Index(fields=['username', 'created_at'], name='auth_login__usernam_28cca0_idx'), models.Index(fields=['ip_address', 'created_at'], name='auth_login__ip_addr_b3ce76_idx'), models.Index(fields=['is_successful', 'created_at'], name='auth_login__is_succ_d07187_idx'), models.Index(fields=['device_fingerprint'], name='auth_login__device__355f4a_idx')],
            },
        ),
        migrations.CreateModel(
            name='IPWhitelist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('description', models.CharField(blank=True, max_length=200, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': 'IP白名单',
                'verbose_name_plural': 'IP白名单',
                'db_table': 'auth_ip_whitelist',
                'indexes': [models.Index(fields=['user', 'is_active'], name='auth_ip_whi_user_id_b87626_idx'), models.Index(fields=['ip_address'], name='auth_ip_whi_ip_addr_246e13_idx')],
                'unique_together': {('user', 'ip_address')},
            },
        ),
    ]
