"""
用户管理模块 - 视图集
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth.hashers import make_password
from django.db.models import Q
from django.utils import timezone
from apps.common.response import ApiResponse
from apps.common.exceptions import BusinessException, ErrorCode
from apps.common.permissions import DataScopeService
from .models import UserProfile
from .serializers import (
    UserSerializer, UserCreateSerializer, UserProfileSerializer,
    PasswordResetSerializer, ChangePasswordSerializer, UserListSerializer
)


class UserViewSet(viewsets.ModelViewSet):
    """用户管理视图集"""
    
    queryset = UserProfile.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return UserCreateSerializer
        elif self.action == 'list':
            return UserListSerializer
        elif self.action in ['profile', 'update_profile']:
            return UserProfileSerializer
        elif self.action == 'reset_password':
            return PasswordResetSerializer
        elif self.action == 'change_password':
            return ChangePasswordSerializer
        return UserSerializer
    
    def get_queryset(self):
        """获取查询集 - 支持搜索和过滤"""
        queryset = UserProfile.objects.all()
        
        # 搜索功能
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(nickname__icontains=search) |
                Q(email__icontains=search) |
                Q(phone__icontains=search)
            )
        
        # 状态过滤
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # 员工状态过滤
        is_staff = self.request.query_params.get('is_staff', None)
        if is_staff is not None:
            queryset = queryset.filter(is_staff=is_staff.lower() == 'true')
        
        # 超级用户过滤
        is_superuser = self.request.query_params.get('is_superuser', None)
        if is_superuser is not None:
            queryset = queryset.filter(is_superuser=is_superuser.lower() == 'true')
        
        # 创建时间范围过滤
        created_start = self.request.query_params.get('created_start', None)
        created_end = self.request.query_params.get('created_end', None)
        if created_start:
            queryset = queryset.filter(created_at__gte=created_start)
        if created_end:
            queryset = queryset.filter(created_at__lte=created_end)
        
        # 排序
        ordering = self.request.query_params.get('ordering', '-created_at')
        if ordering:
            queryset = queryset.order_by(ordering)
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """获取用户列表"""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            
            # 分页
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                paginated_response = self.get_paginated_response(serializer.data)

                # 提取分页信息
                page_info = {
                    'count': paginated_response.data.get('count', 0),
                    'page': int(request.query_params.get('page', 1)),
                    'page_size': self.paginator.page_size,
                    'total_pages': (paginated_response.data.get('count', 0) + self.paginator.page_size - 1) // self.paginator.page_size,
                    'next': paginated_response.data.get('next'),
                    'previous': paginated_response.data.get('previous')
                }

                # 转换为统一API响应格式
                return ApiResponse.paginated_success(
                    data=serializer.data,
                    page_info=page_info,
                    message="用户列表获取成功"
                )
            
            serializer = self.get_serializer(queryset, many=True)
            return ApiResponse.success(
                data=serializer.data,
                message="用户列表获取成功"
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取用户列表失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
    
    def create(self, request, *args, **kwargs):
        """创建用户"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            user = serializer.save()
            
            # 返回创建的用户信息
            response_serializer = UserSerializer(user)
            return ApiResponse.success(
                data=response_serializer.data,
                message="用户创建成功",
                code=1001
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"创建用户失败: {str(e)}",
                code=ErrorCode.USER_CREATE_FAILED
            )
    
    def retrieve(self, request, *args, **kwargs):
        """获取用户详情"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return ApiResponse.success(
                data=serializer.data,
                message="用户详情获取成功"
            )
        except UserProfile.DoesNotExist:
            return ApiResponse.error(
                message="用户不存在",
                code=ErrorCode.USER_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取用户详情失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
    
    def update(self, request, *args, **kwargs):
        """更新用户信息"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            user = serializer.save()
            
            return ApiResponse.success(
                data=serializer.data,
                message="用户信息更新成功"
            )
        except UserProfile.DoesNotExist:
            return ApiResponse.error(
                message="用户不存在",
                code=ErrorCode.USER_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"更新用户信息失败: {str(e)}",
                code=ErrorCode.USER_UPDATE_FAILED
            )
    
    def destroy(self, request, *args, **kwargs):
        """软删除用户"""
        try:
            instance = self.get_object()
            
            # 检查是否为当前用户
            if instance.id == request.user.id:
                return ApiResponse.error(
                    message="不能删除当前登录用户",
                    code=ErrorCode.CANNOT_DELETE_SELF
                )
            
            # 执行软删除
            instance.soft_delete()
            
            return ApiResponse.success(
                message="用户删除成功"
            )
        except UserProfile.DoesNotExist:
            return ApiResponse.error(
                message="用户不存在",
                code=ErrorCode.USER_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"删除用户失败: {str(e)}",
                code=ErrorCode.USER_DELETE_FAILED
            )
    
    @action(detail=False, methods=['get'])
    def profile(self, request):
        """获取当前用户信息"""
        try:
            serializer = self.get_serializer(request.user)
            return ApiResponse.success(
                data=serializer.data,
                message="个人信息获取成功"
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取个人信息失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
    
    @action(detail=False, methods=['put', 'patch'])
    def update_profile(self, request):
        """更新当前用户个人资料"""
        try:
            partial = request.method == 'PATCH'
            serializer = self.get_serializer(
                request.user, 
                data=request.data, 
                partial=partial
            )
            serializer.is_valid(raise_exception=True)
            user = serializer.save()
            
            return ApiResponse.success(
                data=serializer.data,
                message="个人资料更新成功"
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"更新个人资料失败: {str(e)}",
                code=ErrorCode.PROFILE_UPDATE_FAILED
            )
    
    @action(detail=True, methods=['post'])
    def reset_password(self, request, pk=None):
        """重置用户密码（管理员功能）"""
        try:
            user = self.get_object()
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # 设置新密码
            new_password = serializer.validated_data['new_password']
            user.set_password(new_password)
            user.login_fail_count = 0  # 重置登录失败次数
            user.locked_until = None   # 解除锁定
            user.save()
            
            return ApiResponse.success(
                message="密码重置成功"
            )
        except UserProfile.DoesNotExist:
            return ApiResponse.error(
                message="用户不存在",
                code=ErrorCode.USER_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"重置密码失败: {str(e)}",
                code=ErrorCode.PASSWORD_RESET_FAILED
            )
    
    @action(detail=False, methods=['post'])
    def change_password(self, request):
        """修改当前用户密码"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # 设置新密码
            new_password = serializer.validated_data['new_password']
            request.user.set_password(new_password)
            request.user.save()
            
            return ApiResponse.success(
                message="密码修改成功"
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"修改密码失败: {str(e)}",
                code=ErrorCode.PASSWORD_CHANGE_FAILED
            )
    
    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        """恢复已删除的用户"""
        try:
            # 从所有对象中查找（包括已删除的）
            user = UserProfile.all_objects.get(pk=pk)
            
            if not user.is_deleted:
                return ApiResponse.error(
                    message="用户未被删除，无需恢复",
                    code=ErrorCode.USER_NOT_DELETED
                )
            
            # 恢复用户
            user.restore()
            
            return ApiResponse.success(
                message="用户恢复成功"
            )
        except UserProfile.DoesNotExist:
            return ApiResponse.error(
                message="用户不存在",
                code=ErrorCode.USER_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"恢复用户失败: {str(e)}",
                code=ErrorCode.USER_RESTORE_FAILED
            )
    
    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """切换用户激活状态"""
        try:
            user = self.get_object()
            
            # 检查是否为当前用户
            if user.id == request.user.id:
                return ApiResponse.error(
                    message="不能修改当前登录用户的激活状态",
                    code=ErrorCode.CANNOT_MODIFY_SELF
                )
            
            user.is_active = not user.is_active
            user.save()
            
            status_text = "激活" if user.is_active else "禁用"
            return ApiResponse.success(
                data={'is_active': user.is_active},
                message=f"用户{status_text}成功"
            )
        except UserProfile.DoesNotExist:
            return ApiResponse.error(
                message="用户不存在",
                code=ErrorCode.USER_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"切换用户状态失败: {str(e)}",
                code=ErrorCode.USER_STATUS_CHANGE_FAILED
            )
