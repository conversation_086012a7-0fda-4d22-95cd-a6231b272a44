# Generated by Django 4.2.23 on 2025-07-28 08:26

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OperationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('LOGIN', '登录'), ('LOGOUT', '登出'), ('CREATE', '创建'), ('UPDATE', '更新'), ('DELETE', '删除'), ('QUERY', '查询')], max_length=10, verbose_name='操作类型')),
                ('operation_desc', models.CharField(max_length=500, verbose_name='操作描述')),
                ('method', models.CharField(max_length=10, verbose_name='请求方法')),
                ('path', models.Char<PERSON><PERSON>(max_length=500, verbose_name='请求路径')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('status_code', models.IntegerField(verbose_name='状态码')),
                ('response_time', models.IntegerField(verbose_name='响应时间(ms)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='操作时间')),
            ],
            options={
                'verbose_name': '操作日志',
                'verbose_name_plural': '操作日志',
                'db_table': 'sys_operation_log',
            },
        ),
    ]
