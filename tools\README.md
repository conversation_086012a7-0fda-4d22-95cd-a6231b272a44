# HEIM项目工具集

本目录包含HEIM项目开发过程中使用的各种工具和脚本。

## 📋 工具列表

### 1. task_manager.py - 任务管理工具
统一管理任务执行和Git工作流程的主要工具。

**功能:**
- 自动创建功能分支
- 管理任务状态
- 执行标准Git工作流程
- 生成规范的提交信息
- 自动清理分支和创建标签

**使用方法:**
```bash
# 开始新任务
python tools/task_manager.py start 3 "统一响应格式和错误处理机制"

# 完成任务
python tools/task_manager.py complete 3
```

### 2. git_helper.py - Git操作辅助工具
提供Git工作流程的底层操作函数。

**功能:**
- Git分支管理
- 提交和合并操作
- 标签管理
- 远程仓库操作

**使用方法:**
```bash
# 查看Git状态
python tools/git_helper.py status

# 查看分支列表
python tools/git_helper.py branches

# 查看当前分支
python tools/git_helper.py current-branch
```

## 🔄 工作流程

### 标准任务开发流程

1. **开始任务**: 使用 `task_manager.py start` 创建功能分支
2. **开发过程**: 在功能分支上进行开发，定期提交
3. **完成任务**: 使用 `task_manager.py complete` 执行完整的Git工作流程

### Git操作选项

任务完成时，系统会询问Git操作选项：
- **[Y]** 是，提交并推送到远程仓库 - 完整的Git工作流程
- **[N]** 否，稍后手动处理 - 跳过Git操作
- **[L]** 仅本地提交，不推送 - 只做本地操作

## 📁 文件结构

```
tools/
├── README.md           # 本说明文件
├── task_manager.py     # 任务管理主工具
└── git_helper.py       # Git操作辅助工具
```

## 🎯 设计原则

1. **统一入口**: 通过task_manager.py提供统一的任务管理入口
2. **模块化**: git_helper.py提供可复用的Git操作函数
3. **自动化**: 减少手动操作，提高开发效率
4. **标准化**: 确保所有开发者遵循相同的工作流程
5. **可扩展**: 便于添加新的工具和功能

## 🔧 扩展开发

如需添加新工具，请遵循以下规范：
- 使用Python编写
- 添加完整的文档字符串
- 提供命令行接口
- 更新本README文件