"""
部门管理模块 - URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import DepartmentViewSet

# 创建路由器
router = DefaultRouter()
router.register(r'departments', DepartmentViewSet, basename='department')

# URL模式
urlpatterns = [
    # 部门管理API
    path('', include(router.urls)),
]

# 可用的API端点：
# GET    /api/departments/                    - 获取部门列表（支持搜索、过滤、分页）
# POST   /api/departments/                    - 创建新部门
# GET    /api/departments/{id}/               - 获取部门详情
# PUT    /api/departments/{id}/               - 更新部门信息（完整更新）
# PATCH  /api/departments/{id}/               - 更新部门信息（部分更新）
# DELETE /api/departments/{id}/               - 软删除部门
# 
# GET    /api/departments/tree/               - 获取部门树形结构
# GET    /api/departments/statistics/         - 获取部门统计信息
# 
# GET    /api/departments/{id}/members/       - 获取部门成员列表
# POST   /api/departments/{id}/add_member/    - 添加部门成员
# PUT    /api/departments/{id}/update_member/ - 更新部门成员信息
# PATCH  /api/departments/{id}/update_member/ - 更新部门成员信息（部分更新）
# DELETE /api/departments/{id}/remove_member/ - 移除部门成员
# 
# POST   /api/departments/{id}/restore/       - 恢复已删除的部门
# POST   /api/departments/{id}/toggle_active/ - 切换部门激活状态
# GET    /api/departments/{id}/path/          - 获取部门完整路径
#
# 查询参数说明：
# - search: 搜索关键词（部门名称、编码、描述）
# - is_active: 过滤激活状态 (true/false)
# - parent_id: 过滤父部门ID (数字或'null'表示根部门)
# - level: 过滤部门层级
# - ordering: 排序字段 (默认: tree_id,lft)
# - page: 页码
# - page_size: 每页数量
#
# 部门成员查询参数：
# - is_manager: 过滤主管状态 (true/false)
# - is_primary: 过滤主部门状态 (true/false)
#
# 部门树形结构说明：
# - 返回完整的部门树形结构，包含所有层级的部门
# - 每个部门包含其子部门列表
# - 包含部门成员数量和主管信息
#
# 部门成员管理说明：
# - 支持添加、更新、移除部门成员
# - 支持设置主管级别和权重
# - 支持设置兼职的生效和失效日期
# - 支持多部门兼职和多级主管
#
# 删除保护机制：
# - 有子部门的部门不能删除
# - 有成员的部门不能删除
# - 删除前会检查并提示具体的阻止原因
#
# 数据验证：
# - 部门编码唯一性验证
# - 父子关系循环引用检查
# - 主管级别和权重验证
# - 日期有效性验证
