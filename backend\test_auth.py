#!/usr/bin/env python
"""
认证功能测试脚本
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000/api/auth"

def test_captcha():
    """测试验证码生成"""
    print("=== 测试验证码生成 ===")
    response = requests.get(f"{BASE_URL}/captcha/")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    if response.status_code == 200:
        data = response.json()['data']
        return data['captcha_key'], data['captcha_question']
    return None, None

def test_login(captcha_key, captcha_answer):
    """测试用户登录"""
    print("\n=== 测试用户登录 ===")
    data = {
        'username': 'testuser',
        'password': 'testpass123',
        'captcha': captcha_answer,
        'captcha_key': captcha_key
    }
    
    response = requests.post(f"{BASE_URL}/login/", json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    if response.status_code == 200:
        data = response.json()['data']
        return data['access'], data['refresh']
    return None, None

def test_refresh_token(refresh_token):
    """测试令牌刷新"""
    print("\n=== 测试令牌刷新 ===")
    data = {
        'refresh': refresh_token
    }
    
    response = requests.post(f"{BASE_URL}/refresh/", json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    if response.status_code == 200:
        data = response.json()['data']
        return data['access']
    return None

def test_auth_status(access_token):
    """测试认证状态查询"""
    print("\n=== 测试认证状态查询 ===")
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    response = requests.get(f"{BASE_URL}/status/", headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

    if response.status_code == 200:
        data = response.json()['data']
        return data.get('active_sessions', [])
    return []

def test_sessions_list(access_token):
    """测试会话列表查询"""
    print("\n=== 测试会话列表查询 ===")
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    response = requests.get(f"{BASE_URL}/sessions/", headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

def test_logout(access_token, refresh_token):
    """测试用户登出"""
    print("\n=== 测试用户登出 ===")
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    data = {
        'refresh': refresh_token
    }

    response = requests.post(f"{BASE_URL}/logout/", json=data, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

def main():
    """主测试流程"""
    print("开始测试JWT认证系统...")
    
    # 1. 测试验证码生成
    captcha_key, captcha_question = test_captcha()
    if not captcha_key:
        print("验证码生成失败，退出测试")
        return
    
    # 计算验证码答案（简单的加法）
    try:
        parts = captcha_question.split(' + ')
        captcha_answer = str(int(parts[0]) + int(parts[1]))
        print(f"验证码问题: {captcha_question}")
        print(f"验证码答案: {captcha_answer}")
    except:
        print("无法解析验证码问题")
        return
    
    # 2. 测试登录
    access_token, refresh_token = test_login(captcha_key, captcha_answer)
    if not access_token:
        print("登录失败，退出测试")
        return
    
    # 3. 测试认证状态查询
    active_sessions = test_auth_status(access_token)

    # 4. 测试会话列表查询
    test_sessions_list(access_token)

    # 5. 测试令牌刷新
    new_access_token = test_refresh_token(refresh_token)
    if new_access_token:
        access_token = new_access_token

    # 6. 测试登出
    test_logout(access_token, refresh_token)
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
