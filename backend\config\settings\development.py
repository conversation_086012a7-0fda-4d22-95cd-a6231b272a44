"""
开发环境配置
"""
from .base import *

# 开发环境特定配置
DEBUG = True

# 数据库配置 - 开发环境使用SQLite
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# 邮件配置 - 开发环境使用控制台后端
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# 缓存配置 - 开发环境使用本地内存缓存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# 开发工具（可选）
try:
    import django_extensions
    INSTALLED_APPS += ['django_extensions']
except ImportError:
    pass

# 调试工具栏（可选）
if DEBUG:
    try:
        import debug_toolbar
        INSTALLED_APPS.append('debug_toolbar')
        MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')
        INTERNAL_IPS = ['127.0.0.1', 'localhost']
    except ImportError:
        pass

# 日志配置 - 开发环境更详细的日志
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['loggers']['apps']['level'] = 'DEBUG'

# CORS配置 - 开发环境允许所有来源
CORS_ALLOW_ALL_ORIGINS = True