#!/usr/bin/env python
"""
Git操作辅助工具
提供Git工作流程的底层操作函数
"""
import subprocess
import sys
from pathlib import Path


class GitHelper:
    """Git操作辅助类"""
    
    def __init__(self, project_root=None):
        self.project_root = project_root or Path(__file__).parent.parent
        
    def get_current_branch(self):
        """获取当前分支名"""
        try:
            result = subprocess.run(['git', 'branch', '--show-current'], 
                                  cwd=self.project_root, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            return None
    
    def has_uncommitted_changes(self):
        """检查是否有未提交的变更"""
        try:
            result = subprocess.run(['git', 'status', '--porcelain'], 
                                  cwd=self.project_root, capture_output=True, text=True, check=True)
            return bool(result.stdout.strip())
        except subprocess.CalledProcessError:
            return False
    
    def create_branch(self, branch_name, base_branch='develop'):
        """创建新分支"""
        try:
            # 切换到基础分支
            subprocess.run(['git', 'checkout', base_branch], 
                         cwd=self.project_root, check=True)
            
            # 拉取最新代码
            subprocess.run(['git', 'pull', 'origin', base_branch], 
                         cwd=self.project_root, check=True)
            
            # 创建新分支
            subprocess.run(['git', 'checkout', '-b', branch_name], 
                         cwd=self.project_root, check=True)
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建分支失败: {e}")
            return False
    
    def commit_changes(self, message):
        """提交变更"""
        try:
            # 添加所有变更
            subprocess.run(['git', 'add', '.'], cwd=self.project_root, check=True)
            
            # 提交变更
            subprocess.run(['git', 'commit', '-m', message], 
                         cwd=self.project_root, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 提交失败: {e}")
            return False
    
    def merge_branch(self, branch_name, target_branch='develop', no_ff=True):
        """合并分支"""
        try:
            # 切换到目标分支
            subprocess.run(['git', 'checkout', target_branch], 
                         cwd=self.project_root, check=True)
            
            # 拉取最新代码
            subprocess.run(['git', 'pull', 'origin', target_branch], 
                         cwd=self.project_root, check=True)
            
            # 合并分支
            merge_cmd = ['git', 'merge', branch_name]
            if no_ff:
                merge_cmd.append('--no-ff')
            subprocess.run(merge_cmd, cwd=self.project_root, check=True)
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 合并分支失败: {e}")
            return False
    
    def delete_branch(self, branch_name, force=False):
        """删除分支"""
        try:
            delete_cmd = ['git', 'branch', '-d' if not force else '-D', branch_name]
            subprocess.run(delete_cmd, cwd=self.project_root, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 删除分支失败: {e}")
            return False
    
    def create_tag(self, tag_name, message):
        """创建标签"""
        try:
            subprocess.run(['git', 'tag', '-a', tag_name, '-m', message], 
                         cwd=self.project_root, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建标签失败: {e}")
            return False
    
    def push_to_remote(self, branch_name='develop', tags=False):
        """推送到远程仓库"""
        try:
            # 推送分支
            subprocess.run(['git', 'push', 'origin', branch_name], 
                         cwd=self.project_root, check=True)
            
            # 推送标签
            if tags:
                subprocess.run(['git', 'push', 'origin', '--tags'], 
                             cwd=self.project_root, check=True)
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 推送失败: {e}")
            return False
    
    def delete_remote_branch(self, branch_name):
        """删除远程分支"""
        try:
            subprocess.run(['git', 'push', 'origin', '--delete', branch_name], 
                         cwd=self.project_root, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"⚠️ 删除远程分支失败: {e}")
            return False
    
    def get_branch_list(self, remote=False):
        """获取分支列表"""
        try:
            cmd = ['git', 'branch']
            if remote:
                cmd.append('-r')
            
            result = subprocess.run(cmd, cwd=self.project_root, 
                                  capture_output=True, text=True, check=True)
            
            branches = []
            for line in result.stdout.split('\n'):
                line = line.strip()
                if line and not line.startswith('*'):
                    branches.append(line)
                elif line.startswith('*'):
                    branches.append(line[2:])  # 移除 '* ' 前缀
            
            return branches
        except subprocess.CalledProcessError:
            return []
    
    def generate_commit_message(self, task_name, task_number, details=None):
        """生成规范的提交信息"""
        message = f"feat: 完成任务{task_number} - {task_name}\n\n"
        message += f"✨ 新功能:\n- 完成任务{task_number}: {task_name}\n\n"
        
        if details:
            message += f"📋 详细内容:\n"
            for detail in details:
                message += f"- {detail}\n"
            message += "\n"
        
        message += f"📋 任务进度:\n- 完成任务{task_number}: {task_name}\n\n"
        message += "🧪 测试: 功能已通过验证"
        
        return message


def main():
    """命令行接口"""
    if len(sys.argv) < 2:
        print("Git辅助工具")
        print("用法: python git_helper.py <command> [args...]")
        print("\n可用命令:")
        print("  status          - 显示Git状态")
        print("  branches        - 显示分支列表")
        print("  current-branch  - 显示当前分支")
        sys.exit(1)
    
    command = sys.argv[1]
    git_helper = GitHelper()
    
    if command == 'status':
        current_branch = git_helper.get_current_branch()
        has_changes = git_helper.has_uncommitted_changes()
        
        print(f"当前分支: {current_branch}")
        print(f"未提交变更: {'是' if has_changes else '否'}")
        
    elif command == 'branches':
        branches = git_helper.get_branch_list()
        print("本地分支:")
        for branch in branches:
            print(f"  {branch}")
            
    elif command == 'current-branch':
        branch = git_helper.get_current_branch()
        print(branch if branch else "无法获取当前分支")
        
    else:
        print(f"未知命令: {command}")
        sys.exit(1)


if __name__ == '__main__':
    main()