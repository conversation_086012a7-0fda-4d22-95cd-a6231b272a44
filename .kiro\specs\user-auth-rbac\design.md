# 设计文档

## 概述

HEIM企业管理平台用户认证与权限管理系统采用Django + Vue3前后端分离架构，实现JWT认证、RBAC权限控制、多部门兼职和数据范围权限。系统支持2000用户规模，具备高安全性和可扩展性。

## 项目目录结构

### 后端结构

```
backend/
├── manage.py                 # Django管理脚本
├── pyproject.toml           # uv项目配置和依赖管理
├── .env                     # 环境变量
├── config/                  # 项目配置
│   ├── __init__.py
│   ├── settings/            # 分环境配置
│   │   ├── __init__.py
│   │   ├── base.py         # 基础配置
│   │   ├── development.py  # 开发环境
│   │   └── production.py   # 生产环境
│   ├── urls.py             # 主路由配置
│   ├── wsgi.py             # WSGI配置
│   └── asgi.py             # ASGI配置
├── apps/                    # 应用模块
│   ├── __init__.py
│   ├── common/             # 公共模块
│   │   ├── __init__.py
│   │   ├── models.py       # 基础模型
│   │   ├── permissions.py  # 权限服务
│   │   ├── response.py     # 统一响应
│   │   ├── exceptions.py   # 异常处理
│   │   └── middleware.py   # 中间件
│   ├── authentication/     # 认证模块
│   │   ├── models.py       # 会话模型
│   │   ├── views.py        # 认证视图
│   │   ├── serializers.py  # 序列化器
│   │   ├── services.py     # 业务服务
│   │   └── urls.py         # 路由配置
│   ├── users/              # 用户管理
│   │   ├── models.py       # 用户模型
│   │   ├── views.py        # 用户视图
│   │   ├── serializers.py  # 序列化器
│   │   └── urls.py         # 路由配置
│   ├── departments/        # 部门管理
│   │   ├── models.py       # 部门模型
│   │   ├── views.py        # 部门视图
│   │   ├── serializers.py  # 序列化器
│   │   └── urls.py         # 路由配置
│   ├── permissions/        # 权限管理
│   │   ├── models.py       # 权限模型
│   │   ├── views.py        # 权限视图
│   │   ├── serializers.py  # 序列化器
│   │   └── urls.py         # 路由配置
│   └── audit/              # 审计日志
│       ├── models.py       # 日志模型
│       ├── views.py        # 日志视图
│       ├── serializers.py  # 序列化器
│       └── urls.py         # 路由配置
├── static/                 # 静态文件
├── media/                  # 媒体文件
├── logs/                   # 日志文件
└── templates/              # 模板文件
```

### 前端结构

```
frontend/
├── public/                 # 公共资源
├── src/
│   ├── main.ts            # 应用入口
│   ├── App.vue            # 根组件
│   ├── style.css          # 全局样式
│   ├── api/               # API接口
│   │   ├── index.ts       # API配置
│   │   ├── auth.ts        # 认证接口
│   │   ├── users.ts       # 用户接口
│   │   ├── departments.ts # 部门接口
│   │   ├── permissions.ts # 权限接口
│   │   └── audit.ts       # 审计接口
│   ├── components/        # 通用组件
│   │   ├── common/        # 基础组件
│   │   ├── layout/        # 布局组件
│   │   └── forms/         # 表单组件
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证页面
│   │   ├── users/         # 用户管理
│   │   ├── departments/   # 部门管理
│   │   ├── permissions/   # 权限管理
│   │   └── audit/         # 审计日志
│   ├── router/            # 路由配置
│   │   ├── index.ts       # 路由定义
│   │   └── guards.ts      # 路由守卫
│   ├── stores/            # 状态管理
│   │   ├── auth.ts        # 认证状态
│   │   ├── user.ts        # 用户状态
│   │   └── app.ts         # 应用状态
│   ├── types/             # 类型定义
│   │   ├── auth.ts        # 认证类型
│   │   ├── user.ts        # 用户类型
│   │   ├── department.ts  # 部门类型
│   │   └── api.ts         # API类型
│   ├── utils/             # 工具函数
│   │   ├── request.ts     # HTTP请求
│   │   ├── auth.ts        # 认证工具
│   │   └── common.ts      # 通用工具
│   ├── directives/        # 自定义指令
│   │   └── permission.ts  # 权限指令
│   └── assets/            # 静态资源
├── .env                   # 环境变量
├── .env.production        # 生产环境变量
├── vite.config.ts         # Vite配置
├── tailwind.config.js     # Tailwind配置
├── tsconfig.json          # TypeScript配置
└── package.json           # 项目配置
```

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层 (Vue3 + TypeScript)"
        A[登录页面] --> B[主应用]
        B --> C[用户管理]
        B --> D[部门管理]
        B --> E[角色权限]
        B --> F[审计日志]
    end
    
    subgraph "API网关层"
        G[JWT认证中间件]
        H[权限验证中间件]
        I[审计日志中间件]
    end
    
    subgraph "业务逻辑层 (Django)"
        J[认证服务]
        K[用户服务]
        L[部门服务]
        M[权限服务]
        N[审计服务]
    end
    
    subgraph "数据访问层"
        O[用户仓储]
        P[部门仓储]
        Q[权限仓储]
        R[日志仓储]
    end
    
    subgraph "数据存储层"
        S[(SQLite/SQL Server)]
        T[日志文件]
    end
    
    A --> G
    B --> G
    G --> H
    H --> I
    I --> J
    I --> K
    I --> L
    I --> M
    I --> N
    J --> O
    K --> O
    L --> P
    M --> Q
    N --> R
    O --> S
    P --> S
    Q --> S
    R --> S
    N --> T
```

### 技术栈架构

**后端技术栈：**
- Python 3.12+ (运行时环境)
- Django 4.2+ (Web框架 LTS版本)
- Django REST Framework 3.16+ (API框架)
- django-guardian 2.4+ (对象级权限控制)
- djangorestframework-simplejwt (JWT认证)
- django-mptt (树形结构支持，部门层级)
- django-cors-headers (跨域支持)
- celery (异步任务处理)
- redis (缓存和消息队列)
- uv (Python包管理工具，替代pip)

**前端技术栈：**
- Node.js 22.0+ (运行时环境)
- Vue 3.x (前端框架，Composition API)
- TypeScript (类型安全)
- Vite (构建工具和开发服务器)
- Pinia (状态管理，替代Vuex)
- Vue Router 4 (路由管理)
- Naive UI (UI组件库)
- Tailwind CSS (原子化CSS框架)
- Axios (HTTP客户端)
- PNPM (包管理器，比npm更快)

## 组件和接口设计

### 数据模型设计

#### 基础模型 (BaseModel)

```python
from django.db import models
from django.utils import timezone

class BaseModel(models.Model):
    """基础模型 - 提供通用字段和软删除功能"""
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    is_deleted = models.BooleanField(default=False, verbose_name="是否删除")
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name="删除时间")
    
    class Meta:
        abstract = True
    
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()
    
    def restore(self):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None
        self.save()

class ActiveManager(models.Manager):
    """活跃对象管理器 - 过滤已删除对象"""
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)
```

#### 1. 用户模型 (UserProfile)

```python
from django.contrib.auth.models import AbstractUser
from django.db import models

class UserProfile(AbstractUser, BaseModel):
    """扩展用户模型"""
    # 基础字段
    username = models.CharField(max_length=150, unique=True, verbose_name="用户名")
    nickname = models.CharField(max_length=100, verbose_name="昵称")
    email = models.EmailField(blank=True, verbose_name="邮箱")
    
    # 预留扩展字段
    phone = models.CharField(max_length=11, blank=True, null=True, verbose_name="手机号")
    wechat_work_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="企业微信ID")
    
    # 用户信息
    avatar = models.URLField(blank=True, verbose_name="头像")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 状态管理
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name="最后登录IP")
    last_login_time = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")
    login_fail_count = models.IntegerField(default=0, verbose_name="登录失败次数")
    locked_until = models.DateTimeField(null=True, blank=True, verbose_name="锁定到期时间")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()  # 包含已删除对象
    
    class Meta:
        db_table = 'auth_user_profile'
        verbose_name = "用户"
        verbose_name_plural = "用户"
```

#### 1.1 用户会话管理模型 (UserSession)

```python
class UserSession(BaseModel):
    """用户会话管理 - 控制并发登录"""
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, verbose_name="用户")
    session_key = models.CharField(max_length=40, unique=True, verbose_name="会话密钥")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(verbose_name="用户代理")
    
    # 会话状态
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    last_activity = models.DateTimeField(auto_now=True, verbose_name="最后活动时间")
    expires_at = models.DateTimeField(verbose_name="过期时间")
    
    # 设备信息
    device_type = models.CharField(max_length=50, blank=True, verbose_name="设备类型")
    browser = models.CharField(max_length=100, blank=True, verbose_name="浏览器")
    os = models.CharField(max_length=100, blank=True, verbose_name="操作系统")
    
    class Meta:
        db_table = 'auth_user_session'
        verbose_name = "用户会话"
        verbose_name_plural = "用户会话"
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
```

#### 2. 部门模型 (Department)

```python
from mptt.models import MPTTModel, TreeForeignKey

class Department(MPTTModel, BaseModel):
    """部门模型 - 支持多主管"""
    name = models.CharField(max_length=100, verbose_name="部门名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="部门编码")
    parent = TreeForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, 
                           related_name='children', verbose_name="上级部门")
    
    # 部门信息
    description = models.TextField(blank=True, verbose_name="部门描述")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class MPTTMeta:
        order_insertion_by = ['sort_order', 'name']
    
    class Meta:
        db_table = 'sys_department'
        verbose_name = "部门"
        verbose_name_plural = "部门"
    
    def get_managers(self, level=None):
        """获取部门主管"""
        queryset = self.userdepartment_set.filter(is_manager=True, is_deleted=False)
        if level:
            queryset = queryset.filter(manager_level=level)
        return queryset.order_by('manager_level', 'weight')
    
    def get_primary_manager(self):
        """获取主要主管（一级主管中权重最高的）"""
        return self.get_managers(level=1).first()
```

#### 3. 用户部门关联模型 (UserDepartment)

```python
from django.utils import timezone

class UserDepartment(BaseModel):
    """用户部门关联 - 支持多部门兼职和多主管"""
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, verbose_name="用户")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="部门")
    
    # 关联属性
    is_primary = models.BooleanField(default=False, verbose_name="是否主部门")
    is_manager = models.BooleanField(default=False, verbose_name="是否部门主管")
    position = models.CharField(max_length=100, blank=True, verbose_name="职位")
    
    # 主管级别（支持多级主管）
    MANAGER_LEVEL_CHOICES = [
        (1, '一级主管'),
        (2, '二级主管'),
        (3, '三级主管'),
    ]
    manager_level = models.IntegerField(
        choices=MANAGER_LEVEL_CHOICES, 
        null=True, 
        blank=True, 
        verbose_name="主管级别"
    )
    
    # 权重（用于权限计算时的优先级）
    weight = models.IntegerField(default=1, verbose_name="权重")
    
    # 生效时间
    effective_date = models.DateField(default=timezone.now, verbose_name="生效日期")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="失效日期")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_user_department'
        unique_together = ['user', 'department']
        verbose_name = "用户部门关联"
        verbose_name_plural = "用户部门关联"
        
        # 数据库约束：确保每个用户只有一个主部门
        constraints = [
            models.UniqueConstraint(
                fields=['user'], 
                condition=models.Q(is_primary=True, is_deleted=False),
                name='unique_primary_department_per_user'
            )
        ]
        
        indexes = [
            models.Index(fields=['user', 'is_manager']),
            models.Index(fields=['department', 'is_manager']),
            models.Index(fields=['effective_date', 'expiry_date']),
        ]
    
    def is_effective(self):
        """检查关联是否在有效期内"""
        now = timezone.now().date()
        if self.effective_date > now:
            return False
        if self.expiry_date and self.expiry_date < now:
            return False
        return True
    
    @classmethod
    def get_effective_relations(cls, user=None, department=None):
        """获取有效的用户部门关联"""
        now = timezone.now().date()
        queryset = cls.objects.filter(
            effective_date__lte=now,
            is_deleted=False
        ).filter(
            models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gte=now)
        )
        
        if user:
            queryset = queryset.filter(user=user)
        if department:
            queryset = queryset.filter(department=department)
            
        return queryset
```

#### 4. 角色模型 (Role)

```python
class Role(BaseModel):
    """角色模型"""
    DATA_SCOPE_CHOICES = [
        ('ALL', '全部数据'),
        ('DEPT_AND_SUB', '本部门及下级'),
        ('DEPT_ONLY', '仅本部门'),
        ('SELF_ONLY', '仅本人'),
        ('CUSTOM', '自定义'),
    ]
    
    name = models.CharField(max_length=100, verbose_name="角色名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="角色编码")
    data_scope = models.CharField(max_length=20, choices=DATA_SCOPE_CHOICES, 
                                 default='SELF_ONLY', verbose_name="数据范围")
    
    # 角色属性
    description = models.TextField(blank=True, verbose_name="角色描述")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    
    # 关联
    permissions = models.ManyToManyField('Permission', blank=True, verbose_name="权限")
    users = models.ManyToManyField(UserProfile, through='UserRole', verbose_name="用户")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_role'
        verbose_name = "角色"
        verbose_name_plural = "角色"
```

#### 5. 权限模型 (Permission)

```python
from django.contrib.contenttypes.models import ContentType

class Permission(BaseModel):
    """权限模型"""
    PERMISSION_TYPE_CHOICES = [
        ('MENU', '菜单权限'),
        ('BUTTON', '按钮权限'),
        ('API', 'API权限'),
    ]
    
    name = models.CharField(max_length=100, verbose_name="权限名称")
    code = models.CharField(max_length=100, unique=True, verbose_name="权限编码")
    permission_type = models.CharField(max_length=10, choices=PERMISSION_TYPE_CHOICES, 
                                     default='MENU', verbose_name="权限类型")
    
    # 权限属性
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, 
                              related_name='children', verbose_name="父权限")
    path = models.CharField(max_length=500, blank=True, verbose_name="路径")  # 增加长度
    component = models.CharField(max_length=200, blank=True, verbose_name="组件")
    icon = models.CharField(max_length=100, blank=True, verbose_name="图标")
    
    # HTTP方法字段
    http_method = models.CharField(max_length=10, blank=True, verbose_name="HTTP方法")
    
    # 其他属性
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_permission'
        verbose_name = "权限"
        verbose_name_plural = "权限"
        indexes = [
            models.Index(fields=['permission_type', 'is_active']),
            models.Index(fields=['parent', 'sort_order']),
        ]
```

#### 6. 用户角色关联模型 (UserRole)

```python
class UserRole(BaseModel):
    """用户角色关联"""
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, verbose_name="用户")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name="角色")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, 
                                  null=True, blank=True, verbose_name="关联部门")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_user_role'
        unique_together = ['user', 'role', 'department']
        verbose_name = "用户角色关联"
        verbose_name_plural = "用户角色关联"
```

#### 7. 操作审计日志模型 (OperationLog)

```python
class OperationLog(models.Model):
    """操作审计日志 - 不继承BaseModel，避免软删除"""
    OPERATION_TYPE_CHOICES = [
        ('LOGIN', '登录'),
        ('LOGOUT', '登出'),
        ('CREATE', '创建'),
        ('UPDATE', '更新'),
        ('DELETE', '删除'),
        ('QUERY', '查询'),
    ]
    
    # 操作信息
    user = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, null=True, verbose_name="操作用户")
    operation_type = models.CharField(max_length=10, choices=OPERATION_TYPE_CHOICES, verbose_name="操作类型")
    operation_desc = models.CharField(max_length=500, verbose_name="操作描述")
    
    # 请求信息
    method = models.CharField(max_length=10, verbose_name="请求方法")
    path = models.CharField(max_length=500, verbose_name="请求路径")
    ip_address = models.GenericIPAddressField(verbose_name="IP地址")
    user_agent = models.TextField(blank=True, verbose_name="用户代理")
    
    # 响应信息
    status_code = models.IntegerField(verbose_name="状态码")
    response_time = models.IntegerField(verbose_name="响应时间(ms)")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="操作时间")
    
    class Meta:
        db_table = 'sys_operation_log'
        verbose_name = "操作日志"
        verbose_name_plural = "操作日志"
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['operation_type', 'created_at']),
        ]
    
    @classmethod
    def cleanup_old_logs(cls, days=90):
        """清理过期日志"""
        from django.utils import timezone
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        return cls.objects.filter(created_at__lt=cutoff_date).delete()
```

### API接口设计

#### 统一响应格式

```python
# common/response.py
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime

class ApiResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(data=None, message="操作成功", code=1000):
        return Response({
            'code': code,
            'message': message,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }, status=status.HTTP_200_OK)
    
    @staticmethod
    def error(message="操作失败", code=5000, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            'code': code,
            'message': message,
            'data': None,
            'timestamp': datetime.now().isoformat()
        }, status=status_code)
```

#### 认证相关API

```python
# apps/authentication/views.py
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.tokens import RefreshToken

@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """用户登录"""
    # POST /api/auth/login
    # {
    #     "username": "admin",
    #     "password": "password",
    #     "captcha": "1234",
    #     "captcha_key": "uuid"
    # }
    pass

@api_view(['POST'])
@permission_classes([AllowAny])
def logout(request):
    """用户登出"""
    # POST /api/auth/logout
    pass

@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_token(request):
    """刷新令牌"""
    # POST /api/auth/refresh
    # {
    #     "refresh": "refresh_token"
    # }
    pass

@api_view(['GET'])
@permission_classes([AllowAny])
def captcha(request):
    """获取图形验证码"""
    # GET /api/auth/captcha
    pass
```

#### 用户管理API

```python
# apps/users/views.py
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action

class UserViewSet(ModelViewSet):
    """用户管理视图集"""
    
    def list(self):
        """获取用户列表"""
        # GET /api/users/
        pass
    
    def create(self):
        """创建用户"""
        # POST /api/users/
        pass
    
    def retrieve(self):
        """获取用户详情"""
        # GET /api/users/{id}/
        pass
    
    def update(self):
        """更新用户"""
        # PUT /api/users/{id}/
        pass
    
    def destroy(self):
        """删除用户"""
        # DELETE /api/users/{id}/
        pass
    
    @action(detail=False, methods=['get'])
    def profile(self, request):
        """获取当前用户信息"""
        # GET /api/users/profile/
        pass
    
    @action(detail=False, methods=['put'])
    def update_profile(self, request):
        """更新当前用户信息"""
        # PUT /api/users/update_profile/
        pass
    
    @action(detail=True, methods=['post'])
    def reset_password(self, request, pk=None):
        """重置用户密码"""
        # POST /api/users/{id}/reset_password/
        pass
```

### 权限验证中间件设计

```python
# common/middleware.py
from django.utils.deprecation import MiddlewareMixin
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError

class JWTAuthenticationMiddleware(MiddlewareMixin):
    """JWT认证中间件"""
    
    def process_request(self, request):
        # 跳过不需要认证的路径
        skip_paths = ['/api/auth/login', '/api/auth/captcha']
        if request.path in skip_paths:
            return None
        
        # JWT令牌验证
        jwt_auth = JWTAuthentication()
        try:
            user, token = jwt_auth.authenticate(request)
            if user:
                request.user = user
        except (InvalidToken, TokenError):
            # 返回401未授权
            pass
        
        return None

class PermissionMiddleware(MiddlewareMixin):
    """权限验证中间件"""
    
    def process_request(self, request):
        # 检查用户权限
        if hasattr(request, 'user') and request.user.is_authenticated:
            # 权限验证逻辑
            pass
        return None

class AuditLogMiddleware(MiddlewareMixin):
    """审计日志中间件"""
    
    def process_response(self, request, response):
        # 记录操作日志
        if hasattr(request, 'user') and request.user.is_authenticated:
            # 异步记录日志
            pass
        return response
```

### 会话管理服务

```python
# apps/authentication/services.py
from django.utils import timezone
from django.conf import settings
from apps.authentication.models import UserSession
import uuid

class SessionService:
    """会话管理服务"""
    
    @staticmethod
    def create_session(user, request):
        """创建用户会话"""
        # 检查并发登录限制
        max_sessions = getattr(settings, 'MAX_USER_SESSIONS', 5)
        active_sessions = UserSession.objects.filter(
            user=user, 
            is_active=True,
            expires_at__gt=timezone.now()
        ).count()
        
        if active_sessions >= max_sessions:
            # 踢出最早的会话
            oldest_session = UserSession.objects.filter(
                user=user, 
                is_active=True
            ).order_by('created_at').first()
            if oldest_session:
                oldest_session.is_active = False
                oldest_session.save()
        
        # 创建新会话
        session = UserSession.objects.create(
            user=user,
            session_key=str(uuid.uuid4()),
            ip_address=SessionService.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            expires_at=timezone.now() + timezone.timedelta(hours=24),
            device_type=SessionService.get_device_type(request),
            browser=SessionService.get_browser(request),
            os=SessionService.get_os(request)
        )
        
        return session
    
    @staticmethod
    def get_client_ip(request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def get_device_type(request):
        """获取设备类型"""
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if 'mobile' in user_agent:
            return 'mobile'
        elif 'tablet' in user_agent:
            return 'tablet'
        return 'desktop'
    
    @staticmethod
    def get_browser(request):
        """获取浏览器信息"""
        # 简化实现，实际可使用user-agents库
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if 'Chrome' in user_agent:
            return 'Chrome'
        elif 'Firefox' in user_agent:
            return 'Firefox'
        elif 'Safari' in user_agent:
            return 'Safari'
        return 'Unknown'
    
    @staticmethod
    def get_os(request):
        """获取操作系统信息"""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if 'Windows' in user_agent:
            return 'Windows'
        elif 'Mac' in user_agent:
            return 'macOS'
        elif 'Linux' in user_agent:
            return 'Linux'
        return 'Unknown'
    
    @staticmethod
    def cleanup_expired_sessions():
        """清理过期会话"""
        return UserSession.objects.filter(
            expires_at__lt=timezone.now()
        ).update(is_active=False)
```

### 数据范围权限服务

```python
# common/permissions.py
from django.db.models import Q
from django.utils import timezone
from apps.departments.models import Department, UserDepartment

class DataScopeService:
    """数据范围权限服务"""
    
    @staticmethod
    def get_data_scope_filter(user, data_scope):
        """根据数据范围获取查询过滤器"""
        if data_scope == 'ALL':
            return Q()  # 无限制
        
        elif data_scope == 'SELF_ONLY':
            return Q(created_by=user)
        
        elif data_scope == 'DEPT_ONLY':
            # 获取用户有效的主部门
            primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                is_primary=True
            ).first()
            if primary_dept:
                return Q(department=primary_dept.department)
            return Q(pk__in=[])  # 无部门则无数据
        
        elif data_scope == 'DEPT_AND_SUB':
            # 获取用户有效的主部门及其子部门
            primary_dept = UserDepartment.get_effective_relations(user=user).filter(
                is_primary=True
            ).first()
            if primary_dept:
                dept_ids = Department.objects.get_descendants(
                    primary_dept.department, include_self=True
                ).values_list('id', flat=True)
                return Q(department_id__in=dept_ids)
            return Q(pk__in=[])
        
        return Q(pk__in=[])  # 默认无数据
    
    @staticmethod
    def get_user_departments(user, include_expired=False):
        """获取用户所属部门"""
        queryset = UserDepartment.objects.filter(user=user, is_deleted=False)
        
        if not include_expired:
            queryset = UserDepartment.get_effective_relations(user=user)
        
        return queryset.select_related('department')
    
    @staticmethod
    def get_user_managed_departments(user, level=None):
        """获取用户管理的部门"""
        queryset = UserDepartment.get_effective_relations(user=user).filter(
            is_manager=True
        )
        
        if level:
            queryset = queryset.filter(manager_level=level)
        
        return queryset.select_related('department').order_by('manager_level', 'weight')
```

## 错误处理

### 错误码定义

```python
# common/exceptions.py
class ErrorCode:
    """错误码定义"""
    # 成功
    SUCCESS = 1000
    
    # 认证错误 (2000-2099)
    LOGIN_FAILED = 2001
    CAPTCHA_ERROR = 2002
    TOKEN_EXPIRED = 2003
    TOKEN_INVALID = 2004
    ACCOUNT_LOCKED = 2005
    ACCOUNT_DISABLED = 2006
    
    # 权限错误 (2100-2199)
    PERMISSION_DENIED = 2101
    INSUFFICIENT_PERMISSIONS = 2102
    
    # 业务错误 (3000-3999)
    USER_NOT_FOUND = 3001
    USER_ALREADY_EXISTS = 3002
    DEPARTMENT_HAS_CHILDREN = 3003
    ROLE_IN_USE = 3004
    
    # 系统错误 (5000-5999)
    INTERNAL_ERROR = 5000
    DATABASE_ERROR = 5001
    NETWORK_ERROR = 5002

class BusinessException(Exception):
    """业务异常"""
    def __init__(self, code, message):
        self.code = code
        self.message = message
        super().__init__(message)
```

### 全局异常处理

```python
# common/exceptions.py
from rest_framework.views import exception_handler
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    response = exception_handler(exc, context)
    
    if isinstance(exc, BusinessException):
        return ApiResponse.error(exc.message, exc.code)
    
    if response is not None:
        # 记录异常日志
        logger.error(f"API异常: {exc}", exc_info=True)
        
        # 返回统一格式
        return ApiResponse.error(
            message="系统异常，请稍后重试",
            code=ErrorCode.INTERNAL_ERROR,
            status_code=response.status_code
        )
    
    return response
```

## 测试策略

### 单元测试

```python
# tests/test_authentication.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

User = get_user_model()

class AuthenticationTestCase(TestCase):
    """认证功能测试"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            nickname='测试用户'
        )
    
    def test_login_success(self):
        """测试登录成功"""
        response = self.client.post('/api/auth/login/', {
            'username': 'testuser',
            'password': 'testpass123',
            'captcha': '1234',
            'captcha_key': 'test-key'
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data['data'])
    
    def test_login_invalid_credentials(self):
        """测试登录凭据错误"""
        response = self.client.post('/api/auth/login/', {
            'username': 'testuser',
            'password': 'wrongpass',
            'captcha': '1234',
            'captcha_key': 'test-key'
        })
        self.assertEqual(response.data['code'], ErrorCode.LOGIN_FAILED)
```

### 集成测试

```python
# tests/test_permissions.py
class PermissionTestCase(TestCase):
    """权限功能集成测试"""
    
    def test_data_scope_permission(self):
        """测试数据范围权限"""
        # 创建测试数据
        # 验证不同数据范围的查询结果
        pass
    
    def test_role_permission_inheritance(self):
        """测试角色权限继承"""
        # 测试多部门角色权限合并
        pass
```

## 前端设计

### 状态管理 (Pinia)

```typescript
// stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginForm } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  const currentDepartment = ref<number | null>(null)
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })
  
  // 方法
  const login = async (form: LoginForm) => {
    // 登录逻辑
  }
  
  const logout = async () => {
    // 登出逻辑
  }
  
  const refreshUserInfo = async () => {
    // 刷新用户信息
  }
  
  return {
    token,
    userInfo,
    permissions,
    currentDepartment,
    isLoggedIn,
    hasPermission,
    login,
    logout,
    refreshUserInfo
  }
})
```

### 路由守卫

```typescript
// router/guards.ts
import { Router } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

export function setupRouterGuards(router: Router) {
  // 认证守卫
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    
    // 检查是否需要认证
    if (to.meta.requiresAuth && !authStore.isLoggedIn) {
      next('/login')
      return
    }
    
    // 检查权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission)) {
      next('/403')
      return
    }
    
    next()
  })
}
```

### 权限指令

```typescript
// directives/permission.ts
import type { Directive } from 'vue'
import { useAuthStore } from '@/stores/auth'

export const vPermission: Directive = {
  mounted(el, binding) {
    const authStore = useAuthStore()
    const permission = binding.value
    
    if (!authStore.hasPermission(permission)) {
      el.style.display = 'none'
    }
  },
  
  updated(el, binding) {
    const authStore = useAuthStore()
    const permission = binding.value
    
    if (!authStore.hasPermission(permission)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}
```

## 部署配置

### Django设置

```python
# config/settings/production.py
import os
from .base import *

# 安全设置
DEBUG = False
ALLOWED_HOSTS = ['your-domain.com']

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'mssql',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT'),
    }
}

# JWT配置
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=2),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/django.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 前端构建配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  build: {
    outDir: 'dist',
    assetsDir: 'static',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['naive-ui']
        }
      }
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

这个设计文档涵盖了系统的核心架构、数据模型、API设计、权限控制、错误处理、测试策略和部署配置。设计遵循了您提供的技术栈要求，并充分考虑了2000用户规模的性能需求和安全要求。