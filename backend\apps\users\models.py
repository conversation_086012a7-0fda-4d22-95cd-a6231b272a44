"""
用户管理模块 - 用户模型
"""
from django.contrib.auth.models import AbstractUser, UserManager
from django.db import models
from apps.common.models import BaseModel, ActiveManager


class ActiveUserManager(UserManager):
    """活跃用户管理器 - 继承UserManager并过滤已删除对象"""
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)


class UserProfile(AbstractUser, BaseModel):
    """扩展用户模型"""
    # 基础字段
    username = models.CharField(max_length=150, unique=True, verbose_name="用户名")
    nickname = models.CharField(max_length=100, verbose_name="昵称")
    email = models.EmailField(blank=True, verbose_name="邮箱")
    
    # 预留扩展字段
    phone = models.CharField(max_length=11, blank=True, null=True, verbose_name="手机号")
    wechat_work_id = models.CharField(max_length=100, blank=True, null=True, verbose_name="企业微信ID")
    
    # 用户信息
    avatar = models.URLField(blank=True, verbose_name="头像")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 状态管理
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name="最后登录IP")
    last_login_time = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")
    login_fail_count = models.IntegerField(default=0, verbose_name="登录失败次数")
    locked_until = models.DateTimeField(null=True, blank=True, verbose_name="锁定到期时间")
    
    # 管理器
    objects = ActiveUserManager()
    all_objects = UserManager()  # 包含已删除对象
    
    class Meta:
        db_table = 'auth_user_profile'
        verbose_name = "用户"
        verbose_name_plural = "用户"
    
    def __str__(self):
        return f"{self.nickname} ({self.username})"