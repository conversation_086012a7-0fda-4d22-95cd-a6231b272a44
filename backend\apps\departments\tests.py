"""
部门管理模块 - 单元测试
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from apps.common.exceptions import ErrorCode
from .models import Department, UserDepartment

User = get_user_model()


class DepartmentModelTestCase(TestCase):
    """部门模型测试"""
    
    def setUp(self):
        """测试数据准备"""
        self.root_dept = Department.objects.create(
            name='总公司',
            code='ROOT',
            description='总公司'
        )
        
        self.tech_dept = Department.objects.create(
            name='技术部',
            code='TECH',
            parent=self.root_dept,
            description='技术部门'
        )
        
        self.dev_dept = Department.objects.create(
            name='开发组',
            code='DEV',
            parent=self.tech_dept,
            description='开发组'
        )
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            nickname='测试用户',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_department_creation(self):
        """测试部门创建"""
        dept = Department.objects.create(
            name='测试部门',
            code='TEST',
            description='测试部门'
        )
        self.assertEqual(dept.name, '测试部门')
        self.assertEqual(dept.code, 'TEST')
        self.assertTrue(dept.is_active)
        self.assertFalse(dept.is_deleted)
    
    def test_department_hierarchy(self):
        """测试部门层级关系"""
        # 测试父子关系
        self.assertEqual(self.tech_dept.parent, self.root_dept)
        self.assertEqual(self.dev_dept.parent, self.tech_dept)
        
        # 测试层级
        self.assertEqual(self.root_dept.level, 0)
        self.assertEqual(self.tech_dept.level, 1)
        self.assertEqual(self.dev_dept.level, 2)
        
        # 测试子部门获取
        children = self.root_dept.get_children()
        self.assertIn(self.tech_dept, children)
        
        # 测试祖先获取
        ancestors = self.dev_dept.get_ancestors()
        self.assertIn(self.root_dept, ancestors)
        self.assertIn(self.tech_dept, ancestors)
    
    def test_department_soft_delete(self):
        """测试部门软删除"""
        self.tech_dept.soft_delete()
        
        self.assertTrue(self.tech_dept.is_deleted)
        self.assertIsNotNone(self.tech_dept.deleted_at)
        
        # 验证活跃管理器不包含已删除部门
        self.assertFalse(Department.objects.filter(id=self.tech_dept.id).exists())
        # 验证全对象管理器包含已删除部门
        self.assertTrue(Department.all_objects.filter(id=self.tech_dept.id).exists())
    
    def test_department_restore(self):
        """测试部门恢复"""
        self.tech_dept.soft_delete()
        self.tech_dept.restore()
        
        self.assertFalse(self.tech_dept.is_deleted)
        self.assertIsNone(self.tech_dept.deleted_at)
        
        # 验证部门重新出现在活跃管理器中
        self.assertTrue(Department.objects.filter(id=self.tech_dept.id).exists())
    
    def test_user_department_relation(self):
        """测试用户部门关联"""
        relation = UserDepartment.objects.create(
            user=self.user,
            department=self.tech_dept,
            is_primary=True,
            is_manager=True,
            manager_level=1,
            position='技术总监'
        )
        
        self.assertEqual(relation.user, self.user)
        self.assertEqual(relation.department, self.tech_dept)
        self.assertTrue(relation.is_primary)
        self.assertTrue(relation.is_manager)
        self.assertEqual(relation.manager_level, 1)
        self.assertTrue(relation.is_effective())
    
    def test_user_department_effective_date(self):
        """测试用户部门关联有效期"""
        # 创建未来生效的关联
        future_date = timezone.now().date() + timezone.timedelta(days=30)
        relation = UserDepartment.objects.create(
            user=self.user,
            department=self.tech_dept,
            effective_date=future_date
        )
        self.assertFalse(relation.is_effective())
        
        # 创建已过期的关联
        past_date = timezone.now().date() - timezone.timedelta(days=30)
        relation.effective_date = past_date
        relation.expiry_date = timezone.now().date() - timezone.timedelta(days=1)
        relation.save()
        self.assertFalse(relation.is_effective())
    
    def test_department_managers(self):
        """测试部门主管获取"""
        # 创建多个主管
        manager1 = UserDepartment.objects.create(
            user=self.user,
            department=self.tech_dept,
            is_manager=True,
            manager_level=1,
            weight=1
        )
        
        user2 = User.objects.create_user(
            username='manager2',
            nickname='主管2',
            password='pass123'
        )
        manager2 = UserDepartment.objects.create(
            user=user2,
            department=self.tech_dept,
            is_manager=True,
            manager_level=2,
            weight=2
        )
        
        # 测试获取所有主管
        managers = self.tech_dept.get_managers()
        self.assertEqual(managers.count(), 2)
        
        # 测试获取一级主管
        level1_managers = self.tech_dept.get_managers(level=1)
        self.assertEqual(level1_managers.count(), 1)
        self.assertEqual(level1_managers.first(), manager1)
        
        # 测试获取主要主管
        primary_manager = self.tech_dept.get_primary_manager()
        self.assertEqual(primary_manager, manager1)


class DepartmentAPITestCase(APITestCase):
    """部门API测试"""
    
    def setUp(self):
        """测试数据准备"""
        self.client = APIClient()
        
        # 创建测试用户
        self.admin_user = User.objects.create_user(
            username='admin',
            nickname='管理员',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )
        
        self.normal_user = User.objects.create_user(
            username='user',
            nickname='普通用户',
            email='<EMAIL>',
            password='userpass123'
        )
        
        # 创建测试部门
        self.root_dept = Department.objects.create(
            name='总公司',
            code='ROOT',
            description='总公司'
        )
        
        self.tech_dept = Department.objects.create(
            name='技术部',
            code='TECH',
            parent=self.root_dept,
            description='技术部门'
        )

        # 创建子部门用于测试删除保护
        self.dev_dept = Department.objects.create(
            name='开发组',
            code='DEV',
            parent=self.tech_dept,
            description='开发组'
        )
        
        # 获取JWT令牌
        self.admin_token = self._get_jwt_token(self.admin_user)
        self.user_token = self._get_jwt_token(self.normal_user)
    
    def _get_jwt_token(self, user):
        """获取JWT令牌"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def _authenticate_as_admin(self):
        """以管理员身份认证"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
    
    def _authenticate_as_user(self):
        """以普通用户身份认证"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')
    
    def test_get_department_list(self):
        """测试获取部门列表"""
        self._authenticate_as_admin()
        url = reverse('department-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        self.assertIn('data', response.data)
    
    def test_get_department_tree(self):
        """测试获取部门树形结构"""
        self._authenticate_as_admin()
        url = reverse('department-tree')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)
        
        # 验证树形结构
        tree_data = response.data['data']
        self.assertIsInstance(tree_data, list)
        
        # 查找根部门
        root_found = False
        for dept in tree_data:
            if dept['code'] == 'ROOT':
                root_found = True
                self.assertIn('children', dept)
                # 验证子部门
                children = dept['children']
                tech_found = any(child['code'] == 'TECH' for child in children)
                self.assertTrue(tech_found)
                break
        self.assertTrue(root_found)
    
    def test_create_department(self):
        """测试创建部门"""
        self._authenticate_as_admin()
        url = reverse('department-list')
        data = {
            'name': '新部门',
            'code': 'NEW_DEPT',
            'parent': self.tech_dept.id,
            'description': '新创建的部门'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], 1001)  # 创建成功码
        
        # 验证部门已创建
        dept = Department.objects.get(code='NEW_DEPT')
        self.assertEqual(dept.name, '新部门')
        self.assertEqual(dept.parent, self.tech_dept)
    
    def test_create_department_duplicate_code(self):
        """测试创建重复编码的部门"""
        self._authenticate_as_admin()
        url = reverse('department-list')
        data = {
            'name': '重复编码部门',
            'code': 'TECH',  # 已存在的编码
            'description': '重复编码的部门'
        }
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_department(self):
        """测试更新部门信息"""
        self._authenticate_as_admin()
        url = reverse('department-detail', kwargs={'pk': self.tech_dept.id})
        data = {
            'name': '更新后的技术部',
            'description': '更新后的描述'
        }
        response = self.client.patch(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证更新成功
        self.tech_dept.refresh_from_db()
        self.assertEqual(self.tech_dept.name, '更新后的技术部')
        self.assertEqual(self.tech_dept.description, '更新后的描述')

    def test_delete_department_with_children(self):
        """测试删除有子部门的部门"""
        self._authenticate_as_admin()
        url = reverse('department-detail', kwargs={'pk': self.tech_dept.id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.DEPARTMENT_HAS_CHILDREN)

    def test_delete_department_with_members(self):
        """测试删除有成员的部门"""
        # 先添加成员到没有子部门的部门（dev_dept）
        UserDepartment.objects.create(
            user=self.normal_user,
            department=self.dev_dept
        )

        self._authenticate_as_admin()
        url = reverse('department-detail', kwargs={'pk': self.dev_dept.id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.DEPARTMENT_HAS_MEMBERS)

    def test_delete_empty_department(self):
        """测试删除空部门"""
        # 创建一个没有子部门和成员的部门
        empty_dept = Department.objects.create(
            name='空部门',
            code='EMPTY',
            parent=self.tech_dept
        )

        self._authenticate_as_admin()
        url = reverse('department-detail', kwargs={'pk': empty_dept.id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证部门被软删除
        empty_dept.refresh_from_db()
        self.assertTrue(empty_dept.is_deleted)

    def test_restore_department(self):
        """测试恢复部门"""
        # 先删除部门
        empty_dept = Department.objects.create(
            name='待恢复部门',
            code='RESTORE',
            parent=self.tech_dept
        )
        empty_dept.soft_delete()

        self._authenticate_as_admin()
        url = reverse('department-restore', kwargs={'pk': empty_dept.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证部门已恢复
        empty_dept.refresh_from_db()
        self.assertFalse(empty_dept.is_deleted)

    def test_toggle_department_active(self):
        """测试切换部门激活状态"""
        self._authenticate_as_admin()
        url = reverse('department-toggle-active', kwargs={'pk': self.tech_dept.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证状态已切换
        self.tech_dept.refresh_from_db()
        self.assertFalse(self.tech_dept.is_active)

    def test_add_department_member(self):
        """测试添加部门成员"""
        self._authenticate_as_admin()
        url = reverse('department-add-member', kwargs={'pk': self.tech_dept.id})
        data = {
            'user_id': self.normal_user.id,
            'is_primary': True,
            'is_manager': True,
            'manager_level': 1,
            'position': '技术主管'
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证成员已添加
        relation = UserDepartment.objects.get(
            user=self.normal_user,
            department=self.tech_dept
        )
        self.assertTrue(relation.is_primary)
        self.assertTrue(relation.is_manager)
        self.assertEqual(relation.manager_level, 1)

    def test_add_duplicate_member(self):
        """测试添加重复成员"""
        # 先添加成员
        UserDepartment.objects.create(
            user=self.normal_user,
            department=self.tech_dept
        )

        self._authenticate_as_admin()
        url = reverse('department-add-member', kwargs={'pk': self.tech_dept.id})
        data = {
            'user_id': self.normal_user.id
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['code'], ErrorCode.USER_ALREADY_IN_DEPARTMENT)

    def test_get_department_members(self):
        """测试获取部门成员"""
        # 添加成员
        UserDepartment.objects.create(
            user=self.normal_user,
            department=self.tech_dept,
            is_manager=True,
            manager_level=1
        )

        self._authenticate_as_admin()
        url = reverse('department-members', kwargs={'pk': self.tech_dept.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证成员列表
        members = response.data['data']
        self.assertEqual(len(members), 1)
        self.assertEqual(members[0]['user_info']['username'], 'user')

    def test_update_department_member(self):
        """测试更新部门成员信息"""
        # 先添加成员
        UserDepartment.objects.create(
            user=self.normal_user,
            department=self.tech_dept
        )

        self._authenticate_as_admin()
        url = reverse('department-update-member', kwargs={'pk': self.tech_dept.id})
        data = {
            'user_id': self.normal_user.id,
            'is_manager': True,
            'manager_level': 2,
            'position': '高级工程师'
        }
        response = self.client.patch(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证更新成功
        relation = UserDepartment.objects.get(
            user=self.normal_user,
            department=self.tech_dept
        )
        self.assertTrue(relation.is_manager)
        self.assertEqual(relation.manager_level, 2)
        self.assertEqual(relation.position, '高级工程师')

    def test_remove_department_member(self):
        """测试移除部门成员"""
        # 先添加成员
        UserDepartment.objects.create(
            user=self.normal_user,
            department=self.tech_dept
        )

        self._authenticate_as_admin()
        url = reverse('department-remove-member', kwargs={'pk': self.tech_dept.id})
        response = self.client.delete(url + f'?user_id={self.normal_user.id}')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证成员已移除（软删除）
        relation = UserDepartment.all_objects.get(
            user=self.normal_user,
            department=self.tech_dept
        )
        self.assertTrue(relation.is_deleted)

    def test_get_department_path(self):
        """测试获取部门路径"""
        self._authenticate_as_admin()
        url = reverse('department-path', kwargs={'pk': self.tech_dept.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证路径信息
        path_data = response.data['data']
        self.assertIn('path', path_data)
        self.assertIn('总公司 > 技术部', path_data['path'])

    def test_get_department_statistics(self):
        """测试获取部门统计信息"""
        self._authenticate_as_admin()
        url = reverse('department-statistics')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['code'], ErrorCode.SUCCESS)

        # 验证统计信息
        stats = response.data['data']
        self.assertIn('total_departments', stats)
        self.assertIn('active_departments', stats)
        self.assertIn('level_statistics', stats)

    def test_unauthorized_access(self):
        """测试未认证访问"""
        url = reverse('department-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
