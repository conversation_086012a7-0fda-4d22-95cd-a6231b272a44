# Generated by Django 4.2.23 on 2025-07-28 08:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('departments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=100, verbose_name='权限名称')),
                ('code', models.Char<PERSON>ield(max_length=100, unique=True, verbose_name='权限编码')),
                ('permission_type', models.CharField(choices=[('MENU', '菜单权限'), ('BUTTON', '按钮权限'), ('API', 'API权限')], default='MENU', max_length=10, verbose_name='权限类型')),
                ('path', models.CharField(blank=True, max_length=500, verbose_name='路径')),
                ('component', models.CharField(blank=True, max_length=200, verbose_name='组件')),
                ('icon', models.CharField(blank=True, max_length=100, verbose_name='图标')),
                ('http_method', models.CharField(blank=True, max_length=10, verbose_name='HTTP方法')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
            ],
            options={
                'verbose_name': '权限',
                'verbose_name_plural': '权限',
                'db_table': 'sys_permission',
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.CharField(max_length=100, verbose_name='角色名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='角色编码')),
                ('data_scope', models.CharField(choices=[('ALL', '全部数据'), ('DEPT_AND_SUB', '本部门及下级'), ('DEPT_ONLY', '仅本部门'), ('SELF_ONLY', '仅本人'), ('CUSTOM', '自定义')], default='SELF_ONLY', max_length=20, verbose_name='数据范围')),
                ('description', models.TextField(blank=True, verbose_name='角色描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
            ],
            options={
                'verbose_name': '角色',
                'verbose_name_plural': '角色',
                'db_table': 'sys_role',
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='departments.department', verbose_name='关联部门')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='permissions.role', verbose_name='角色')),
            ],
            options={
                'verbose_name': '用户角色关联',
                'verbose_name_plural': '用户角色关联',
                'db_table': 'sys_user_role',
            },
        ),
    ]
