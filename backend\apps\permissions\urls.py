"""
权限管理模块 - URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from apps.permissions.views import PermissionViewSet, RoleViewSet, UserRoleViewSet

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'permissions', PermissionViewSet, basename='permission')
router.register(r'roles', RoleViewSet, basename='role')
router.register(r'user-roles', UserRoleViewSet, basename='user-role')

urlpatterns = [
    # 权限管理API
    path('', include(router.urls)),
]
