# 角色权限管理系统

## 概述

本模块实现了完整的RBAC（基于角色的访问控制）权限管理系统，支持：

- 角色管理（Role）
- 权限管理（Permission）
- 用户角色分配（UserRole）
- 数据范围权限控制
- API级别权限验证
- 权限继承和合并
- 权限缓存优化

## 核心组件

### 1. 模型（Models）

#### Permission（权限模型）
- 支持三种权限类型：MENU（菜单）、BUTTON（按钮）、API（接口）
- 支持树形结构（父子权限关系）
- 包含路径、组件、图标、HTTP方法等属性

#### Role（角色模型）
- 支持五种数据范围：ALL（全部）、DEPT_AND_SUB（本部门及下级）、DEPT_ONLY（本部门）、SELF_ONLY（仅本人）、CUSTOM（自定义）
- 与权限多对多关联
- 支持软删除

#### UserRole（用户角色关联模型）
- 支持按部门分配角色
- 支持多部门兼职
- 软删除支持

### 2. 序列化器（Serializers）

- `PermissionSerializer`: 权限序列化器，支持树形结构
- `PermissionTreeSerializer`: 权限树形序列化器
- `RoleSerializer`: 角色序列化器，处理权限多对多关系
- `UserRoleSerializer`: 用户角色关联序列化器
- `RoleAssignmentSerializer`: 角色分配序列化器

### 3. 视图集（ViewSets）

#### PermissionViewSet
- 权限CRUD操作
- 权限树形结构查询 (`/api/permissions/tree/`)
- 权限类型查询 (`/api/permissions/types/`)

#### RoleViewSet
- 角色CRUD操作
- 数据范围选项查询 (`/api/roles/data_scopes/`)
- 角色权限管理 (`/api/roles/{id}/permissions/`, `/api/roles/{id}/assign_permissions/`)
- 角色用户管理 (`/api/roles/{id}/users/`, `/api/roles/{id}/assign_users/`)

#### UserRoleViewSet
- 用户角色关联管理
- 批量删除 (`/api/user-roles/batch_delete/`)
- 用户权限查询 (`/api/user-roles/user_permissions/`)

### 4. 权限服务（Services）

#### PermissionService
- `get_user_permissions(user)`: 获取用户所有权限
- `get_user_roles(user)`: 获取用户所有角色
- `get_user_data_scope(user)`: 获取用户数据范围
- `check_user_permission(user, permission_code)`: 检查用户权限
- `get_user_menu_permissions(user)`: 获取用户菜单权限
- `get_user_button_permissions(user)`: 获取用户按钮权限
- `get_user_api_permissions(user)`: 获取用户API权限

#### RoleService
- `check_role_can_delete(role)`: 检查角色是否可删除
- `get_role_users(role)`: 获取角色用户
- `assign_role_to_users(role, user_ids, department_id)`: 批量分配角色

### 5. 权限验证装饰器（Decorators）

#### 函数装饰器
```python
from apps.permissions.decorators import require_permission, require_permissions, require_role, require_data_scope

@require_permission('user:create')
def create_user(request):
    pass

@require_permissions('user:create', 'user:edit', require_all=False)
def manage_user(request):
    pass

@require_role('admin')
def admin_only_view(request):
    pass

@require_data_scope('ALL')
def get_all_data(request):
    pass
```

#### 类混入
```python
from apps.permissions.decorators import PermissionMixin

class MyView(PermissionMixin, View):
    required_permissions = ['user:create', 'user:edit']
    required_role = 'admin'
    required_data_scope = 'DEPT_AND_SUB'
    require_all_permissions = True
```

### 6. 权限中间件（Middleware）

`PermissionMiddleware` 自动进行API级别的权限验证：
- 检查用户API权限
- 支持路径通配符匹配
- 超级管理员自动跳过检查

## 使用示例

### 1. 初始化权限数据

```bash
python manage.py init_permissions
```

### 2. 创建角色并分配权限

```python
from apps.permissions.models import Role, Permission

# 创建角色
role = Role.objects.create(
    name='编辑员',
    code='editor',
    data_scope='DEPT_ONLY',
    description='内容编辑员'
)

# 分配权限
permissions = Permission.objects.filter(code__in=['user:view', 'user:edit'])
role.permissions.set(permissions)
```

### 3. 为用户分配角色

```python
from apps.permissions.models import UserRole
from apps.users.models import UserProfile

user = UserProfile.objects.get(username='testuser')
role = Role.objects.get(code='editor')

# 分配角色（全局）
UserRole.objects.create(user=user, role=role)

# 分配角色（指定部门）
from apps.departments.models import Department
department = Department.objects.get(code='tech')
UserRole.objects.create(user=user, role=role, department=department)
```

### 4. 检查用户权限

```python
from apps.permissions.services import PermissionService

# 检查单个权限
has_permission = PermissionService.check_user_permission(user, 'user:create')

# 获取用户所有权限
permissions = PermissionService.get_user_permissions(user)

# 获取用户菜单权限（用于前端菜单生成）
menu_permissions = PermissionService.get_user_menu_permissions(user)

# 获取用户按钮权限
button_permissions = PermissionService.get_user_button_permissions(user)
```

### 5. 在视图中使用权限验证

```python
from apps.permissions.decorators import require_permission
from rest_framework.decorators import api_view

@api_view(['POST'])
@require_permission('user:create')
def create_user_api(request):
    # 只有拥有 user:create 权限的用户才能访问
    pass
```

## API接口

### 权限管理
- `GET /api/permissions/` - 权限列表
- `POST /api/permissions/` - 创建权限
- `GET /api/permissions/{id}/` - 权限详情
- `PUT /api/permissions/{id}/` - 更新权限
- `DELETE /api/permissions/{id}/` - 删除权限
- `GET /api/permissions/tree/` - 权限树
- `GET /api/permissions/types/` - 权限类型

### 角色管理
- `GET /api/roles/` - 角色列表
- `POST /api/roles/` - 创建角色
- `GET /api/roles/{id}/` - 角色详情
- `PUT /api/roles/{id}/` - 更新角色
- `DELETE /api/roles/{id}/` - 删除角色
- `GET /api/roles/data_scopes/` - 数据范围选项
- `GET /api/roles/{id}/permissions/` - 角色权限
- `POST /api/roles/{id}/assign_permissions/` - 分配权限
- `GET /api/roles/{id}/users/` - 角色用户
- `POST /api/roles/{id}/assign_users/` - 分配用户

### 用户角色管理
- `GET /api/user-roles/` - 用户角色列表
- `POST /api/user-roles/` - 创建用户角色关联
- `DELETE /api/user-roles/{id}/` - 删除用户角色关联
- `POST /api/user-roles/batch_delete/` - 批量删除
- `GET /api/user-roles/user_permissions/` - 用户权限查询

## 缓存机制

权限系统使用Redis缓存来提高性能：
- 用户权限缓存（5分钟）
- 用户角色缓存（5分钟）
- 用户数据范围缓存（5分钟）
- 用户菜单权限缓存（10分钟）

缓存会在以下情况自动清除：
- 用户角色发生变化
- 角色权限发生变化
- 权限数据发生变化

## 测试

运行权限管理模块的测试：

```bash
python manage.py test apps.permissions.tests
```

测试覆盖：
- 模型测试
- 服务测试
- API测试
- 权限验证测试

## 注意事项

1. **超级管理员**: `is_superuser=True` 的用户会跳过所有权限检查
2. **软删除**: 所有模型都支持软删除，删除的数据不会影响权限计算
3. **缓存**: 权限数据会被缓存，修改权限后需要等待缓存过期或手动清除
4. **数据范围**: 数据范围权限需要配合具体的业务逻辑使用
5. **API权限**: API权限支持通配符匹配，如 `/api/users/*` 匹配所有用户相关API
