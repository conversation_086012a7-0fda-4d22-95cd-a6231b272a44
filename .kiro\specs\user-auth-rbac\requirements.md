# 需求文档

## 介绍

HEIM企业管理平台的用户认证与基础权限管理系统，支持JWT认证、RBAC权限控制、多部门兼职和数据范围权限。该系统为2000用户规模的企业提供安全可靠的身份认证和细粒度权限控制。

## 需求

### 需求 1：用户数据模型设计

**用户故事：** 作为系统架构师，我希望设计灵活的用户数据模型，支持当前和未来的认证方式扩展，以便系统能够适应不同的业务需求。

#### 验收标准

1. 当设计用户模型时，系统应当包含username字段作为唯一标识符
2. 当设计用户模型时，系统应当包含nickname字段存储用户中文姓名
3. 当设计用户模型时，系统应当预留phone字段支持未来手机验证码登录
4. 当设计用户模型时，系统应当预留wechat_work_id字段支持未来企业微信登录
5. 当设计用户模型时，系统应当包含email、avatar等基础用户信息字段
6. 当设计用户模型时，系统应当包含is_active、last_login_ip等状态管理字段
7. 当用户注册或创建时，系统应当以username作为唯一性验证

### 需求 2：用户认证管理

**用户故事：** 作为系统管理员，我希望能够管理用户账户，支持多种登录方式，包括创建、修改、禁用用户，以便控制系统访问权限。

#### 验收标准

1. 当管理员创建新用户时，系统应当验证用户名唯一性并生成用户账户（包含用户名、昵称、预留手机号和企业微信ID字段）
2. 当用户使用用户名/密码登录时，系统应当验证凭据和图形验证码并返回JWT令牌
3. 当用户使用手机验证码登录时，系统应当验证手机号和短信验证码（预留接口，暂不实现）
4. 当用户使用企业微信扫码登录时，系统应当验证企业微信授权并绑定用户账户（预留接口，暂不实现）
5. 当JWT令牌过期时，系统应当拒绝访问并返回相应错误信息
6. 当用户被禁用时，系统应当立即阻止该用户的所有访问请求
7. 当用户修改个人信息时，系统应当验证数据有效性并更新用户资料

### 需求 3：部门组织架构管理

**用户故事：** 作为HR管理员，我希望能够创建和管理树形部门结构，支持用户在多个部门兼职，以便反映真实的组织架构。

#### 验收标准

1. 当创建部门时，系统应当支持设置父级部门形成树形结构
2. 当删除部门时，如果存在子部门，系统应当阻止删除操作
3. 当用户加入部门时，系统应当支持设置主部门和兼职部门
4. 当设置部门主管时，系统应当验证该用户是否属于该部门
5. 当查询部门成员时，系统应当返回包含兼职人员的完整列表

### 需求 4：角色权限管理

**用户故事：** 作为系统管理员，我希望能够创建角色并分配权限，将角色分配给用户，以便实现灵活的权限控制。

#### 验收标准

1. 当创建角色时，系统应当允许设置角色名称、编码和数据范围
2. 当为角色分配权限时，系统应当支持菜单权限和操作权限的组合
3. 当用户被分配角色时，系统应当合并用户在所有部门的角色权限
4. 当检查用户权限时，系统应当根据用户角色和数据范围返回准确结果
5. 当角色被删除时，如果有用户使用该角色，系统应当阻止删除操作

### 需求 5：数据范围权限控制

**用户故事：** 作为业务用户，我希望只能看到和操作我有权限的数据范围内的信息，以便保护数据安全和隐私。

#### 验收标准

1. 当用户查询数据时，系统应当根据数据范围权限过滤结果
2. 当数据范围为"仅本人"时，系统应当只返回用户自己创建的数据
3. 当数据范围为"本部门"时，系统应当返回用户主部门的数据
4. 当数据范围为"本部门及下级"时，系统应当返回部门树中当前部门及其子部门的数据
5. 当数据范围为"全部"时，系统应当返回所有数据（需要相应权限）

### 需求 6：前端权限控制

**用户故事：** 作为最终用户，我希望界面只显示我有权限访问的菜单和功能按钮，以便获得清晰的用户体验。

#### 验收标准

1. 当用户登录后，系统应当根据用户权限动态生成菜单结构
2. 当用户访问无权限页面时，系统应当显示403错误页面
3. 当页面包含操作按钮时，系统应当根据权限控制按钮的显示和禁用状态
4. 当用户权限发生变化时，系统应当实时更新界面权限状态
5. 当用户在多个部门有不同权限时，系统应当提供部门切换功能

### 需求 7：操作审计与系统日志

**用户故事：** 作为安全管理员，我希望能够查看用户的关键操作记录和系统运行日志，以便进行安全审计和问题追踪。

#### 验收标准

1. 当用户执行关键操作时，系统应当将操作记录存储到数据库中（包含操作时间、用户、IP地址、请求路径、操作结果）
2. 当查询操作审计日志时，系统应当支持按用户、时间范围、操作类型进行筛选
3. 当系统发生运行时异常时，系统应当将错误信息记录到日志文件中便于问题排查
4. 当系统正常运行时，系统应当将访问日志、性能日志记录到文件中
5. 当数据库审计日志达到存储限制时，系统应当自动清理过期记录
6. 当日志文件达到大小限制时，系统应当自动进行日志轮转
7. 当导出审计报告时，系统应当从数据库生成包含关键操作信息的报告文件

### 需求 8：系统安全性

**用户故事：** 作为系统管理员，我希望系统具备基本的安全防护能力，以便保护企业数据安全。

#### 验收标准

1. 当用户多次登录失败时，系统应当临时锁定账户防止暴力破解
2. 当检测到异常登录行为时，系统应当记录并可选择性地通知管理员
3. 当API接口被调用时，系统应当验证JWT令牌的有效性和完整性
4. 当用户上传文件时，系统应当验证文件类型和大小限制
5. 当系统配置敏感信息时，系统应当使用环境变量而非硬编码存储