"""
URL configuration for HEI<PERSON> Auth project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    # 认证相关API
    path('api/auth/', include('apps.authentication.urls')),
    # 用户管理API
    path('api/', include('apps.users.urls')),
    # 部门管理API
    path('api/', include('apps.departments.urls')),
    # 权限管理API
    path('api/', include('apps.permissions.urls')),
    # 测试API（开发阶段）
    path('api/common/', include('apps.common.urls')),
]

# 开发环境静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

    # Django Debug Toolbar
    try:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
    except ImportError:
        pass