# -*- coding: utf-8 -*-
"""
测试视图 - 用于验证异常处理系统
"""
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny

from .response import ApiResponse
from .exceptions import BusinessException, ErrorCode
from .utils import raise_business_error, ResponseHelper


class TestExceptionView(APIView):
    """测试异常处理的API视图"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        """测试成功响应"""
        return ApiResponse.success(
            data={'message': '异常处理系统工作正常'},
            message="测试成功"
        )
    
    def post(self, request):
        """测试业务异常"""
        exception_type = request.data.get('type', 'business')
        
        if exception_type == 'business':
            raise_business_error(
                code=ErrorCode.USER_NOT_FOUND,
                message="这是一个测试业务异常"
            )
        elif exception_type == 'validation':
            from django.core.exceptions import ValidationError
            raise ValidationError({'username': ['用户名不能为空']})
        elif exception_type == 'system':
            raise Exception("这是一个系统异常")
        else:
            return ResponseHelper.validation_error("无效的异常类型")


@api_view(['GET'])
@permission_classes([AllowAny])
def test_response_helper(request):
    """测试响应助手函数"""
    response_type = request.GET.get('type', 'success')
    
    if response_type == 'success':
        return ResponseHelper.success_with_data(
            data={'test': 'data'},
            message="响应助手测试成功"
        )
    elif response_type == 'created':
        return ResponseHelper.created_success(
            data={'id': 1},
            message="创建成功测试"
        )
    elif response_type == 'error':
        return ResponseHelper.validation_error("参数验证失败测试")
    elif response_type == 'permission':
        return ResponseHelper.permission_denied("权限拒绝测试")
    elif response_type == 'notfound':
        return ResponseHelper.not_found("资源不存在测试")
    else:
        return ResponseHelper.validation_error("无效的响应类型")