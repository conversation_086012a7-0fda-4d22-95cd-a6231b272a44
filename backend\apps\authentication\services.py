"""
认证模块 - 业务服务
"""
import uuid
import re
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken
from apps.authentication.models import SimpleCaptcha
from apps.authentication.models import UserSession
from apps.users.models import UserProfile


class CaptchaService:
    """验证码服务"""
    
    @staticmethod
    def generate_captcha():
        """生成数学验证码"""
        captcha = SimpleCaptcha.generate_captcha()
        return {
            'captcha_key': captcha.key,
            'captcha_question': captcha.question
        }

    @staticmethod
    def cleanup_expired_captcha():
        """清理过期验证码"""
        return SimpleCaptcha.cleanup_expired()


class SessionService:
    """会话管理服务"""
    
    @staticmethod
    def create_session(user, request):
        """创建用户会话"""
        # 检查并发登录限制
        max_sessions = getattr(settings, 'MAX_USER_SESSIONS', 5)
        active_sessions = UserSession.objects.filter(
            user=user, 
            is_active=True,
            expires_at__gt=timezone.now()
        ).count()
        
        if active_sessions >= max_sessions:
            # 踢出最早的会话
            oldest_session = UserSession.objects.filter(
                user=user, 
                is_active=True
            ).order_by('created_at').first()
            if oldest_session:
                oldest_session.is_active = False
                oldest_session.save()
        
        # 创建新会话
        session = UserSession.objects.create(
            user=user,
            session_key=str(uuid.uuid4()),
            ip_address=SessionService.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            expires_at=timezone.now() + timezone.timedelta(hours=24),
            device_type=SessionService.get_device_type(request),
            browser=SessionService.get_browser(request),
            os=SessionService.get_os(request)
        )
        
        return session
    
    @staticmethod
    def get_client_ip(request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    @staticmethod
    def get_device_type(request):
        """获取设备类型"""
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            return 'mobile'
        elif 'tablet' in user_agent or 'ipad' in user_agent:
            return 'tablet'
        return 'desktop'
    
    @staticmethod
    def get_browser(request):
        """获取浏览器信息"""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if 'Chrome' in user_agent and 'Edge' not in user_agent:
            return 'Chrome'
        elif 'Firefox' in user_agent:
            return 'Firefox'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            return 'Safari'
        elif 'Edge' in user_agent:
            return 'Edge'
        return 'Unknown'
    
    @staticmethod
    def get_os(request):
        """获取操作系统信息"""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if 'Windows' in user_agent:
            return 'Windows'
        elif 'Mac' in user_agent:
            return 'macOS'
        elif 'Linux' in user_agent:
            return 'Linux'
        elif 'Android' in user_agent:
            return 'Android'
        elif 'iOS' in user_agent:
            return 'iOS'
        return 'Unknown'
    
    @staticmethod
    def cleanup_expired_sessions():
        """清理过期会话"""
        return UserSession.objects.filter(
            expires_at__lt=timezone.now()
        ).update(is_active=False)
    
    @staticmethod
    def invalidate_user_sessions(user, exclude_session=None):
        """使用户所有会话失效"""
        queryset = UserSession.objects.filter(user=user, is_active=True)
        if exclude_session:
            queryset = queryset.exclude(id=exclude_session.id)
        return queryset.update(is_active=False)


class AuthenticationService:
    """认证服务"""
    
    @staticmethod
    def login(user, request):
        """用户登录"""
        # 更新用户登录信息
        user.last_login_time = timezone.now()
        user.last_login_ip = SessionService.get_client_ip(request)
        user.save()
        
        # 创建会话
        session = SessionService.create_session(user, request)
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token
        
        # 在令牌中添加会话信息
        access['session_id'] = str(session.id)
        refresh['session_id'] = str(session.id)
        
        return {
            'access': str(access),
            'refresh': str(refresh),
            'user': user,
            'session': session
        }
    
    @staticmethod
    def logout(user, refresh_token=None, session_id=None):
        """用户登出"""
        # 将刷新令牌加入黑名单
        if refresh_token:
            try:
                token = RefreshToken(refresh_token)
                token.blacklist()
            except Exception:
                pass  # 令牌可能已经无效
        
        # 使会话失效
        if session_id:
            try:
                session = UserSession.objects.get(id=session_id, user=user)
                session.is_active = False
                session.save()
            except UserSession.DoesNotExist:
                pass
        
        return True
    
    @staticmethod
    def refresh_token(refresh_token):
        """刷新访问令牌"""
        try:
            refresh = RefreshToken(refresh_token)
            access = refresh.access_token
            
            # 检查会话是否有效
            session_id = refresh.get('session_id')
            if session_id:
                try:
                    session = UserSession.objects.get(
                        id=session_id, 
                        is_active=True,
                        expires_at__gt=timezone.now()
                    )
                    # 更新会话活动时间
                    session.last_activity = timezone.now()
                    session.save()
                    
                    # 在新令牌中保持会话信息
                    access['session_id'] = session_id
                except UserSession.DoesNotExist:
                    # 会话无效，拒绝刷新
                    refresh.blacklist()
                    return None
            
            return {
                'access': str(access),
                'refresh': str(refresh) if settings.SIMPLE_JWT.get('ROTATE_REFRESH_TOKENS') else refresh_token
            }
        except Exception:
            return None
