#!/usr/bin/env python
"""
安全功能测试脚本
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000/api/auth"

def test_login_and_get_token():
    """登录并获取令牌"""
    print("=== 获取访问令牌 ===")
    
    # 1. 获取验证码
    response = requests.get(f"{BASE_URL}/captcha/")
    if response.status_code != 200:
        print("获取验证码失败")
        return None
    
    captcha_data = response.json()['data']
    captcha_key = captcha_data['captcha_key']
    captcha_question = captcha_data['captcha_question']
    
    # 计算验证码答案
    parts = captcha_question.split(' + ')
    captcha_answer = str(int(parts[0]) + int(parts[1]))
    
    # 2. 登录
    login_data = {
        'username': 'testuser',
        'password': 'testpass123',
        'captcha': captcha_answer,
        'captcha_key': captcha_key
    }
    
    response = requests.post(f"{BASE_URL}/login/", json=login_data)
    if response.status_code == 200:
        data = response.json()['data']
        print(f"登录成功，获得访问令牌")
        
        # 检查是否有安全警告
        if 'security_warnings' in data:
            print(f"安全警告: {data['security_warnings']}")
        
        return data['access']
    else:
        print(f"登录失败: {response.json()}")
        return None

def test_security_info(access_token):
    """测试安全信息查询"""
    print("\n=== 测试安全信息查询 ===")
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(f"{BASE_URL}/security/", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()['data']
        print(f"最近登录尝试: {len(data['recent_attempts'])} 次")
        print(f"IP白名单: {len(data['ip_whitelist'])} 个")
        print(f"统计信息: {data['statistics']}")
        print(f"安全设置: {data['security_settings']}")
    else:
        print(f"查询失败: {response.json()}")

def test_add_ip_whitelist(access_token):
    """测试添加IP白名单"""
    print("\n=== 测试添加IP白名单 ===")
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'ip_address': '*************',
        'description': '家庭网络',
        'expires_days': 30
    }
    
    response = requests.post(f"{BASE_URL}/security/ip-whitelist/", json=data, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

def test_failed_login_attempts():
    """测试失败登录尝试记录"""
    print("\n=== 测试失败登录尝试记录 ===")

    # 故意使用错误密码登录2次（避免锁定）
    for i in range(2):
        # 获取验证码
        response = requests.get(f"{BASE_URL}/captcha/")
        captcha_data = response.json()['data']
        captcha_key = captcha_data['captcha_key']
        captcha_question = captcha_data['captcha_question']

        # 计算验证码答案
        parts = captcha_question.split(' + ')
        captcha_answer = str(int(parts[0]) + int(parts[1]))

        # 使用错误密码登录
        login_data = {
            'username': 'testuser2',  # 使用不同的用户名避免影响主测试
            'password': 'wrongpassword',
            'captcha': captcha_answer,
            'captcha_key': captcha_key
        }

        response = requests.post(f"{BASE_URL}/login/", json=login_data)
        print(f"第{i+1}次失败登录 - 状态码: {response.status_code}")

def test_auth_status(access_token):
    """测试认证状态"""
    print("\n=== 测试认证状态 ===")
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(f"{BASE_URL}/status/", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()['data']
        print(f"当前会话: {data['current_session']['id']}")
        print(f"活跃会话数: {data['total_active_sessions']}")

def main():
    """主测试流程"""
    print("开始测试JWT认证安全系统...")
    
    # 1. 测试失败登录尝试记录
    test_failed_login_attempts()
    
    # 2. 正常登录获取令牌
    access_token = test_login_and_get_token()
    if not access_token:
        print("无法获取访问令牌，退出测试")
        return
    
    # 3. 测试安全信息查询
    test_security_info(access_token)
    
    # 4. 测试添加IP白名单
    test_add_ip_whitelist(access_token)
    
    # 5. 再次查询安全信息，查看变化
    test_security_info(access_token)
    
    # 6. 测试认证状态
    test_auth_status(access_token)
    
    print("\n安全功能测试完成！")

if __name__ == "__main__":
    main()
