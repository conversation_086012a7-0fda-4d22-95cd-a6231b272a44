# -*- coding: utf-8 -*-
"""
统一API响应格式模块
"""
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime


class ApiResponse:
    """统一API响应格式类"""
    
    @staticmethod
    def success(data=None, message="操作成功", code=1000):
        """
        成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            code: 业务状态码
            
        Returns:
            Response: DRF响应对象
        """
        return Response({
            'code': code,
            'message': message,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }, status=status.HTTP_200_OK)
    
    @staticmethod
    def error(message="操作失败", code=5000, status_code=status.HTTP_400_BAD_REQUEST, data=None):
        """
        错误响应
        
        Args:
            message: 错误消息
            code: 业务错误码
            status_code: HTTP状态码
            data: 额外数据
            
        Returns:
            Response: DRF响应对象
        """
        return Response({
            'code': code,
            'message': message,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }, status=status_code)
    
    @staticmethod
    def paginated_success(data, page_info, message="查询成功", code=1000):
        """
        分页成功响应
        
        Args:
            data: 分页数据
            page_info: 分页信息 (包含 count, page, page_size, total_pages)
            message: 响应消息
            code: 业务状态码
            
        Returns:
            Response: DRF响应对象
        """
        return Response({
            'code': code,
            'message': message,
            'data': {
                'results': data,
                'pagination': page_info
            },
            'timestamp': datetime.now().isoformat()
        }, status=status.HTTP_200_OK)