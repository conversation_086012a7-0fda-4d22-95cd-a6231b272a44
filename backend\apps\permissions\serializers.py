"""
权限管理模块 - 序列化器
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from apps.permissions.models import Role, Permission, UserRole
from apps.departments.models import Department

User = get_user_model()


class PermissionSerializer(serializers.ModelSerializer):
    """权限序列化器"""
    
    # 父权限信息
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    # 子权限数量
    children_count = serializers.SerializerMethodField()
    # 权限路径（用于树形显示）
    permission_path = serializers.SerializerMethodField()
    
    class Meta:
        model = Permission
        fields = [
            'id', 'name', 'code', 'permission_type', 'parent', 'parent_name',
            'path', 'component', 'icon', 'http_method', 'sort_order', 
            'is_active', 'children_count', 'permission_path',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_children_count(self, obj):
        """获取子权限数量"""
        return obj.children.filter(is_deleted=False).count()
    
    def get_permission_path(self, obj):
        """获取权限路径（用于显示层级关系）"""
        path = []
        current = obj
        while current:
            path.insert(0, current.name)
            current = current.parent
        return ' > '.join(path)
    
    def validate_code(self, value):
        """验证权限编码唯一性"""
        if self.instance:
            # 更新时排除自身
            if Permission.objects.filter(code=value, is_deleted=False).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("权限编码已存在")
        else:
            # 创建时检查唯一性
            if Permission.objects.filter(code=value, is_deleted=False).exists():
                raise serializers.ValidationError("权限编码已存在")
        return value
    
    def validate_parent(self, value):
        """验证父权限，防止循环引用"""
        if value and self.instance:
            # 检查是否会形成循环引用
            current = value
            while current:
                if current.id == self.instance.id:
                    raise serializers.ValidationError("不能将自己设置为父权限")
                current = current.parent
        return value


class PermissionTreeSerializer(serializers.ModelSerializer):
    """权限树形序列化器"""
    
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = Permission
        fields = [
            'id', 'name', 'code', 'permission_type', 'path', 'component',
            'icon', 'http_method', 'sort_order', 'is_active', 'children'
        ]
    
    def get_children(self, obj):
        """获取子权限"""
        children = obj.children.filter(is_deleted=False, is_active=True).order_by('sort_order', 'name')
        return PermissionTreeSerializer(children, many=True).data


class RoleSerializer(serializers.ModelSerializer):
    """角色序列化器"""
    
    # 权限信息
    permission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="权限ID列表"
    )
    permissions_info = serializers.SerializerMethodField(read_only=True)
    
    # 用户数量
    user_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Role
        fields = [
            'id', 'name', 'code', 'data_scope', 'description', 'is_active',
            'sort_order', 'permission_ids', 'permissions_info', 'user_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_permissions_info(self, obj):
        """获取角色权限信息"""
        permissions = obj.permissions.filter(is_deleted=False, is_active=True)
        return PermissionSerializer(permissions, many=True).data
    
    def get_user_count(self, obj):
        """获取使用该角色的用户数量"""
        return UserRole.objects.filter(role=obj, is_deleted=False).count()
    
    def validate_code(self, value):
        """验证角色编码唯一性"""
        if self.instance:
            # 更新时排除自身
            if Role.objects.filter(code=value, is_deleted=False).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("角色编码已存在")
        else:
            # 创建时检查唯一性
            if Role.objects.filter(code=value, is_deleted=False).exists():
                raise serializers.ValidationError("角色编码已存在")
        return value
    
    def create(self, validated_data):
        """创建角色"""
        permission_ids = validated_data.pop('permission_ids', [])
        role = Role.objects.create(**validated_data)
        
        # 设置权限
        if permission_ids:
            permissions = Permission.objects.filter(id__in=permission_ids, is_deleted=False)
            role.permissions.set(permissions)
        
        return role
    
    def update(self, instance, validated_data):
        """更新角色"""
        permission_ids = validated_data.pop('permission_ids', None)
        
        # 更新基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新权限
        if permission_ids is not None:
            permissions = Permission.objects.filter(id__in=permission_ids, is_deleted=False)
            instance.permissions.set(permissions)
        
        return instance


class UserRoleSerializer(serializers.ModelSerializer):
    """用户角色关联序列化器"""
    
    # 用户信息
    user_info = serializers.SerializerMethodField()
    # 角色信息
    role_info = serializers.SerializerMethodField()
    # 部门信息
    department_info = serializers.SerializerMethodField()
    
    class Meta:
        model = UserRole
        fields = [
            'id', 'user', 'role', 'department', 'user_info', 'role_info',
            'department_info', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_user_info(self, obj):
        """获取用户信息"""
        return {
            'id': obj.user.id,
            'username': obj.user.username,
            'nickname': obj.user.nickname,
            'email': obj.user.email,
            'is_active': obj.user.is_active
        }
    
    def get_role_info(self, obj):
        """获取角色信息"""
        return {
            'id': obj.role.id,
            'name': obj.role.name,
            'code': obj.role.code,
            'data_scope': obj.role.data_scope,
            'is_active': obj.role.is_active
        }
    
    def get_department_info(self, obj):
        """获取部门信息"""
        if obj.department:
            return {
                'id': obj.department.id,
                'name': obj.department.name,
                'code': obj.department.code,
                'is_active': obj.department.is_active
            }
        return None
    
    def validate(self, attrs):
        """验证用户角色关联"""
        user = attrs.get('user')
        role = attrs.get('role')
        department = attrs.get('department')
        
        # 检查用户是否属于指定部门（如果指定了部门）
        if department:
            from apps.departments.models import UserDepartment
            if not UserDepartment.get_effective_relations(user=user, department=department).exists():
                raise serializers.ValidationError("用户不属于指定部门")
        
        # 检查是否已存在相同的用户角色关联
        queryset = UserRole.objects.filter(
            user=user, role=role, department=department, is_deleted=False
        )
        if self.instance:
            queryset = queryset.exclude(id=self.instance.id)
        
        if queryset.exists():
            raise serializers.ValidationError("该用户在指定部门已拥有此角色")
        
        return attrs


class RoleAssignmentSerializer(serializers.Serializer):
    """角色分配序列化器"""
    
    user_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="用户ID列表"
    )
    role_id = serializers.IntegerField(help_text="角色ID")
    department_id = serializers.IntegerField(required=False, help_text="部门ID（可选）")
    
    def validate_role_id(self, value):
        """验证角色是否存在且有效"""
        try:
            role = Role.objects.get(id=value, is_deleted=False, is_active=True)
        except Role.DoesNotExist:
            raise serializers.ValidationError("角色不存在或已禁用")
        return value
    
    def validate_department_id(self, value):
        """验证部门是否存在且有效"""
        if value:
            try:
                department = Department.objects.get(id=value, is_deleted=False, is_active=True)
            except Department.DoesNotExist:
                raise serializers.ValidationError("部门不存在或已禁用")
        return value
    
    def validate_user_ids(self, value):
        """验证用户是否存在且有效"""
        if not value:
            raise serializers.ValidationError("用户ID列表不能为空")
        
        existing_users = User.objects.filter(id__in=value, is_deleted=False, is_active=True)
        if existing_users.count() != len(value):
            raise serializers.ValidationError("部分用户不存在或已禁用")
        
        return value


class UserPermissionSerializer(serializers.Serializer):
    """用户权限序列化器（用于获取用户的所有权限）"""
    
    user_id = serializers.IntegerField()
    permissions = serializers.SerializerMethodField()
    roles = serializers.SerializerMethodField()
    
    def get_permissions(self, obj):
        """获取用户的所有权限"""
        # 这里会在services中实现具体逻辑
        return []
    
    def get_roles(self, obj):
        """获取用户的所有角色"""
        # 这里会在services中实现具体逻辑
        return []
