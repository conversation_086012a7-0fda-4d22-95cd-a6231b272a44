"""
认证模块 - 异步任务
"""
from celery import shared_task
from django.utils import timezone
from apps.authentication.services import CaptchaService, SessionService
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def cleanup_expired_captcha(self):
    """清理过期验证码的异步任务"""
    try:
        deleted_count, _ = CaptchaService.cleanup_expired_captcha()
        logger.info(f"清理过期验证码完成，删除了 {deleted_count} 个验证码")
        return {
            'status': 'success',
            'deleted_count': deleted_count,
            'timestamp': timezone.now().isoformat()
        }
    except Exception as exc:
        logger.error(f"清理过期验证码失败: {exc}")
        # 重试任务
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True, max_retries=3)
def cleanup_expired_sessions(self):
    """清理过期会话的异步任务"""
    try:
        updated_count = SessionService.cleanup_expired_sessions()
        logger.info(f"清理过期会话完成，更新了 {updated_count} 个会话状态")
        return {
            'status': 'success',
            'updated_count': updated_count,
            'timestamp': timezone.now().isoformat()
        }
    except Exception as exc:
        logger.error(f"清理过期会话失败: {exc}")
        # 重试任务
        raise self.retry(exc=exc, countdown=60)


@shared_task
def cleanup_auth_data():
    """清理所有认证相关过期数据的组合任务"""
    results = {}
    
    # 清理验证码
    try:
        captcha_result = cleanup_expired_captcha.delay()
        results['captcha'] = captcha_result.get(timeout=30)
    except Exception as e:
        logger.error(f"清理验证码任务失败: {e}")
        results['captcha'] = {'status': 'error', 'error': str(e)}
    
    # 清理会话
    try:
        session_result = cleanup_expired_sessions.delay()
        results['sessions'] = session_result.get(timeout=30)
    except Exception as e:
        logger.error(f"清理会话任务失败: {e}")
        results['sessions'] = {'status': 'error', 'error': str(e)}
    
    return results


@shared_task(bind=True)
def send_security_alert(self, user_id, alert_type, details):
    """发送安全警报的异步任务"""
    try:
        from apps.users.models import UserProfile
        
        user = UserProfile.objects.get(id=user_id)
        
        # 这里可以实现发送邮件、短信等通知逻辑
        # 目前只记录日志
        logger.warning(
            f"安全警报 - 用户: {user.username}, "
            f"类型: {alert_type}, "
            f"详情: {details}"
        )
        
        return {
            'status': 'success',
            'user_id': user_id,
            'alert_type': alert_type,
            'timestamp': timezone.now().isoformat()
        }
    except Exception as exc:
        logger.error(f"发送安全警报失败: {exc}")
        raise self.retry(exc=exc, countdown=60, max_retries=3)


@shared_task
def check_suspicious_login_attempts():
    """检查可疑登录尝试的任务"""
    try:
        from apps.users.models import UserProfile
        from django.db.models import Count
        
        # 查找在过去1小时内失败登录次数超过10次的用户
        suspicious_users = UserProfile.objects.filter(
            login_fail_count__gte=10,
            updated_at__gte=timezone.now() - timezone.timedelta(hours=1)
        )
        
        for user in suspicious_users:
            # 发送安全警报
            send_security_alert.delay(
                user_id=user.id,
                alert_type='suspicious_login',
                details=f"用户在1小时内失败登录 {user.login_fail_count} 次"
            )
        
        return {
            'status': 'success',
            'suspicious_users_count': suspicious_users.count(),
            'timestamp': timezone.now().isoformat()
        }
    except Exception as e:
        logger.error(f"检查可疑登录尝试失败: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }
