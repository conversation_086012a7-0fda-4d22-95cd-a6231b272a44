"""
认证模块 - URL路由配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from apps.authentication.views import (
    captcha_view, login_view, refresh_token_view, logout_view,
    logout_all_view, UserSessionViewSet, auth_status_view,
    force_logout_session_view, security_info_view, add_ip_whitelist_view
)

# 创建路由器
router = DefaultRouter()
router.register(r'sessions', UserSessionViewSet, basename='session')

app_name = 'authentication'

urlpatterns = [
    # 认证相关接口
    path('captcha/', captcha_view, name='captcha'),
    path('login/', login_view, name='login'),
    path('refresh/', refresh_token_view, name='refresh'),
    path('logout/', logout_view, name='logout'),
    path('logout-all/', logout_all_view, name='logout-all'),
    path('status/', auth_status_view, name='auth-status'),
    path('sessions/<int:session_id>/force-logout/', force_logout_session_view, name='force-logout-session'),
    path('security/', security_info_view, name='security-info'),
    path('security/ip-whitelist/', add_ip_whitelist_view, name='add-ip-whitelist'),

    # 会话管理接口
    path('', include(router.urls)),
]
