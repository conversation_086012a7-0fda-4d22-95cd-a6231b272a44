@echo off
echo 启动HEIM权限管理系统开发环境...

echo.
echo 启动后端Django服务器...
start "Django Backend" cmd /k "cd backend && uv run python manage.py runserver 8000"

echo.
echo 等待3秒后启动前端开发服务器...
timeout /t 3 /nobreak > nul

echo 启动前端Vue开发服务器...
start "Vue Frontend" cmd /k "cd frontend && pnpm dev"

echo.
echo 开发环境启动完成！
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:8000
echo 后端管理: http://localhost:8000/admin
pause