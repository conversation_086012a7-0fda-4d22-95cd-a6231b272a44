# HEIM权限管理系统

基于Django + Vue3的企业级用户权限管理系统

## 项目结构

```
HEIM/
├── backend/          # Django后端
│   ├── apps/         # 应用模块
│   │   ├── common/   # 公共模块
│   │   ├── authentication/  # 认证模块
│   │   ├── users/    # 用户管理
│   │   ├── departments/     # 部门管理
│   │   ├── permissions/     # 权限管理
│   │   └── audit/    # 审计日志
│   ├── config/       # 项目配置
│   │   ├── settings/ # 分环境配置
│   │   ├── urls.py   # 路由配置
│   │   ├── wsgi.py   # WSGI配置
│   │   └── asgi.py   # ASGI配置
│   ├── static/       # 静态文件
│   ├── media/        # 媒体文件
│   ├── logs/         # 日志文件
│   └── manage.py     # Django管理脚本
├── frontend/         # Vue3前端
│   ├── src/          # 源代码
│   └── package.json
├── tools/            # 开发工具
│   ├── task_manager.py  # 任务管理工具
│   ├── git_helper.py    # Git操作辅助工具
│   └── README.md        # 工具说明文档
├── GIT_WORKFLOW.md   # Git工作流程指南
└── start-dev.bat     # 开发环境启动脚本
```

## 技术栈

### 后端
- Django 4.2+
- Django REST Framework 3.16+
- JWT认证
- SQLite数据库（开发环境）
- Redis缓存
- Celery异步任务

### 前端
- Vue 3 + TypeScript
- Vite构建工具
- Naive UI组件库
- Tailwind CSS
- Pinia状态管理
- Vue Router路由

## 快速开始

### 环境要求
- Python 3.12+
- Node.js 18+
- uv (Python包管理器)
- pnpm (Node.js包管理器)

### 启动开发环境

1. 双击运行 `start-dev.bat` 脚本
2. 或者手动启动：

```bash
# 启动后端
cd backend
uv run python manage.py runserver 8000

# 启动前端
cd frontend
pnpm dev
```

### 访问地址
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000/api
- Django管理后台: http://localhost:8000/admin

## 开发说明

### 后端配置
- 配置文件位于 `backend/config/settings/`
- 环境变量配置在 `backend/.env`
- 依赖管理使用 `pyproject.toml`（uv包管理器）
- API接口遵循RESTful规范

### 依赖管理
- **后端**: 使用 `uv` 管理Python依赖，配置在 `pyproject.toml`
- **前端**: 使用 `pnpm` 管理Node.js依赖，配置在 `package.json`

```bash
# 后端添加依赖
cd backend
uv add package-name

# 前端添加依赖
cd frontend
pnpm add package-name
```

### 前端配置
- 开发代理配置在 `frontend/vite.config.ts`
- 环境变量配置在 `frontend/.env`
- 组件库使用Naive UI

### 跨域配置
- 后端CORS已配置允许前端开发服务器访问
- 前端Vite代理已配置转发API请求到后端

## 开发工具

### 任务管理工具
项目提供了自动化的任务管理工具，简化开发流程：

```bash
# 开始新任务（自动创建功能分支）
python tools/task_manager.py start 3 "统一响应格式和错误处理机制"

# 完成任务（自动执行Git工作流程）
python tools/task_manager.py complete 3
```

### Git工作流程
- 采用Git Flow分支策略
- 自动化分支管理和合并
- 规范化提交信息格式
- 详细说明请参考 [GIT_WORKFLOW.md](GIT_WORKFLOW.md)

### 工具说明
更多工具使用说明请参考 [tools/README.md](tools/README.md)

## 下一步

项目环境搭建完成，可以开始实施具体的功能模块开发。