# Generated by Django 4.2.23 on 2025-07-30 01:59

import apps.users.models
import django.contrib.auth.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='userprofile',
            managers=[
                ('objects', apps.users.models.ActiveUserManager()),
                ('all_objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
