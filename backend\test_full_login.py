#!/usr/bin/env python
import os
import django
import requests

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.authentication.serializers import LoginSerializer
from apps.authentication.services import AuthenticationService
from apps.authentication.security import SecurityService
from django.test import RequestFactory

# 获取验证码
response = requests.get('http://127.0.0.1:8000/api/auth/captcha/')
captcha_data = response.json()['data']
captcha_key = captcha_data['captcha_key']
captcha_question = captcha_data['captcha_question']

# 计算验证码答案
parts = captcha_question.split(' + ')
captcha_answer = str(int(parts[0]) + int(parts[1]))

print(f'验证码问题: {captcha_question}')
print(f'验证码答案: {captcha_answer}')

# 测试序列化器
login_data = {
    'username': 'testuser',
    'password': 'testpass123',
    'captcha': captcha_answer,
    'captcha_key': captcha_key
}

print(f'登录数据: {login_data}')

serializer = LoginSerializer(data=login_data)
print(f'序列化器验证结果: {serializer.is_valid()}')

if not serializer.is_valid():
    print(f'验证错误: {serializer.errors}')
else:
    print(f'验证成功')
    user = serializer.validated_data['user']
    print(f'用户: {user}')
    
    # 创建模拟请求
    factory = RequestFactory()
    request = factory.post('/api/auth/login/', login_data)
    request.META['HTTP_USER_AGENT'] = 'Test Agent'
    request.META['REMOTE_ADDR'] = '127.0.0.1'
    
    try:
        # 测试安全策略
        security_results = SecurityService.apply_security_policies(user, request)
        print(f'安全策略结果: {security_results}')
        
        # 测试登录服务
        login_result = AuthenticationService.login(user, request)
        print(f'登录成功: {login_result["access"][:50]}...')
        
    except Exception as e:
        print(f'登录过程中出错: {e}')
        import traceback
        traceback.print_exc()
