#!/usr/bin/env python
"""
HEIM项目任务管理工具
统一管理任务执行和Git工作流程
"""
import os
import sys
import subprocess
import json
from pathlib import Path


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.tasks_file = self.project_root / ".kiro/specs/user-auth-rbac/tasks.md"
        
    def start_task(self, task_number, task_name):
        """
        开始新任务
        
        Args:
            task_number: 任务编号
            task_name: 任务名称
        """
        print(f"\n🚀 开始任务{task_number}: {task_name}")
        
        # 创建功能分支
        branch_name = self._create_feature_branch(task_name, task_number)
        
        # 更新任务状态为进行中
        self._update_task_status(task_number, "in_progress")
        
        print(f"✅ 任务{task_number}已开始")
        print(f"📋 功能分支: {branch_name}")
        print(f"📝 任务状态已更新为进行中")
        print("\n现在可以开始开发了！")
        
    def complete_task(self, task_number, task_name=None):
        """
        完成任务
        
        Args:
            task_number: 任务编号
            task_name: 任务名称（可选）
        """
        if not task_name:
            task_name = self._get_task_name(task_number)
            
        print(f"\n✅ 任务{task_number}完成！")
        
        # 询问Git操作
        choice = self._ask_git_operation(task_name, task_number)
        
        if choice == 'Y':
            self._execute_git_workflow(task_name, task_number, push_to_remote=True)
        elif choice == 'L':
            self._execute_git_workflow(task_name, task_number, push_to_remote=False)
        elif choice == 'N':
            print("跳过Git操作，请稍后手动处理")
        
        # 更新任务状态为完成
        self._update_task_status(task_number, "completed")
        
        print(f"\n🎉 任务{task_number}处理完成！")
        print("🚀 准备继续下一个任务...")
        
    def _create_feature_branch(self, task_name, task_number):
        """创建功能分支"""
        try:
            # 确保在develop分支
            subprocess.run(['git', 'checkout', 'develop'], 
                         cwd=self.project_root, check=True)
            print("✓ 已切换到develop分支")
            
            # 拉取最新代码
            subprocess.run(['git', 'pull', 'origin', 'develop'], 
                         cwd=self.project_root, check=True)
            print("✓ 已拉取最新的develop分支")
            
            # 创建功能分支
            branch_name = f"feature/task-{task_number}-{self._slugify(task_name)}"
            subprocess.run(['git', 'checkout', '-b', branch_name], 
                         cwd=self.project_root, check=True)
            print(f"✓ 已创建并切换到功能分支: {branch_name}")
            
            return branch_name
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建功能分支失败: {e}")
            sys.exit(1)
            
    def _execute_git_workflow(self, task_name, task_number, push_to_remote=True):
        """执行Git工作流程"""
        try:
            # 获取当前分支名
            result = subprocess.run(['git', 'branch', '--show-current'], 
                                  cwd=self.project_root, capture_output=True, text=True, check=True)
            current_branch = result.stdout.strip()
            
            # 检查Git状态
            result = subprocess.run(['git', 'status', '--porcelain'], 
                                  cwd=self.project_root, capture_output=True, text=True, check=True)
            
            if not result.stdout.strip():
                print("没有需要提交的变更")
                return
            
            # 添加所有变更
            subprocess.run(['git', 'add', '.'], cwd=self.project_root, check=True)
            print("✓ 已添加所有变更到暂存区")
            
            # 生成规范的提交信息
            commit_message = f"""feat: 完成任务{task_number} - {task_name}

✨ 新功能:
- 完成任务{task_number}: {task_name}

📋 任务进度:
- 完成任务{task_number}: {task_name}

🧪 测试: 功能已通过验证"""
            
            # 提交变更
            subprocess.run(['git', 'commit', '-m', commit_message], 
                         cwd=self.project_root, check=True)
            print(f"✓ 已提交变更: 任务{task_number}")
            
            # 如果在功能分支上，合并到develop
            if current_branch.startswith('feature/'):
                print("🔄 执行分支合并流程...")
                
                # 切换到develop分支
                subprocess.run(['git', 'checkout', 'develop'], 
                             cwd=self.project_root, check=True)
                print("✓ 已切换到develop分支")
                
                # 拉取最新的develop
                if push_to_remote:
                    subprocess.run(['git', 'pull', 'origin', 'develop'], 
                                 cwd=self.project_root, check=True)
                    print("✓ 已拉取最新的develop分支")
                
                # 合并功能分支（使用--no-ff保留分支历史）
                subprocess.run(['git', 'merge', current_branch, '--no-ff'], 
                             cwd=self.project_root, check=True)
                print(f"✓ 已合并功能分支: {current_branch}")
                
                # 删除功能分支
                subprocess.run(['git', 'branch', '-d', current_branch], 
                             cwd=self.project_root, check=True)
                print(f"✓ 已删除本地功能分支: {current_branch}")
            
            # 创建标签
            tag_name = f"v0.{task_number}.0-task{task_number}"
            tag_message = f"里程碑: {task_name}完成"
            
            try:
                subprocess.run(['git', 'tag', '-a', tag_name, '-m', tag_message], 
                             cwd=self.project_root, check=True)
                print(f"✓ 已创建标签: {tag_name}")
            except subprocess.CalledProcessError:
                print(f"⚠️ 标签 {tag_name} 已存在")
            
            # 推送到远程仓库
            if push_to_remote:
                subprocess.run(['git', 'push', 'origin', 'develop'], 
                             cwd=self.project_root, check=True)
                print("✓ 已推送develop分支到远程仓库")
                
                subprocess.run(['git', 'push', 'origin', tag_name], 
                             cwd=self.project_root, check=True)
                print(f"✓ 已推送标签: {tag_name}")
                
                # 如果删除了功能分支，也删除远程分支
                if current_branch.startswith('feature/'):
                    try:
                        subprocess.run(['git', 'push', 'origin', '--delete', current_branch], 
                                     cwd=self.project_root, check=True)
                        print(f"✓ 已删除远程功能分支: {current_branch}")
                    except subprocess.CalledProcessError:
                        print(f"⚠️ 远程分支 {current_branch} 不存在或已删除")
            
            print(f"\n🎉 任务{task_number}的Git工作流程完成！")
            print("📋 当前状态:")
            print(f"   - 当前分支: develop")
            print(f"   - 标签: {tag_name}")
            print(f"   - 功能分支已清理")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Git操作失败: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ 执行错误: {e}")
            sys.exit(1)
    
    def _ask_git_operation(self, task_name, task_number):
        """询问Git操作选项"""
        print(f"\n✅ 任务{task_number}完成！是否需要提交到Git？")
        print("- [Y] 是，提交并推送到远程仓库")
        print("- [N] 否，稍后手动处理") 
        print("- [L] 仅本地提交，不推送")
        
        while True:
            choice = input("\n请选择 [Y/N/L]: ").upper().strip()
            if choice in ['Y', 'N', 'L']:
                return choice
            print("无效选择，请输入 Y、N 或 L")
    
    def _update_task_status(self, task_number, status):
        """更新任务状态"""
        # 这里可以集成Kiro的taskStatus工具
        # 暂时打印状态更新信息
        status_map = {
            "in_progress": "进行中",
            "completed": "已完成"
        }
        print(f"📝 任务{task_number}状态更新为: {status_map.get(status, status)}")
    
    def _get_task_name(self, task_number):
        """从任务文件获取任务名称"""
        # 简化实现，实际可以解析tasks.md文件
        task_names = {
            1: "项目环境搭建和基础配置",
            2: "数据模型设计和数据库迁移", 
            3: "统一响应格式和错误处理机制",
            4: "JWT认证系统实现",
            5: "用户管理功能开发"
        }
        return task_names.get(task_number, f"任务{task_number}")
    
    def _slugify(self, text):
        """将文本转换为URL友好的格式"""
        return text.lower().replace(' ', '-').replace('和', 'and')


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python task_manager.py start <任务编号> <任务名称>")
        print("  python task_manager.py complete <任务编号> [任务名称]")
        print("\n示例:")
        print("  python task_manager.py start 3 '统一响应格式和错误处理机制'")
        print("  python task_manager.py complete 3")
        sys.exit(1)
    
    command = sys.argv[1]
    task_manager = TaskManager()
    
    if command == 'start':
        if len(sys.argv) < 4:
            print("错误: start命令需要任务编号和任务名称")
            sys.exit(1)
        task_number = int(sys.argv[2])
        task_name = sys.argv[3]
        task_manager.start_task(task_number, task_name)
        
    elif command == 'complete':
        if len(sys.argv) < 3:
            print("错误: complete命令需要任务编号")
            sys.exit(1)
        task_number = int(sys.argv[2])
        task_name = sys.argv[3] if len(sys.argv) > 3 else None
        task_manager.complete_task(task_number, task_name)
        
    else:
        print(f"错误: 未知命令 '{command}'")
        print("支持的命令: start, complete")
        sys.exit(1)


if __name__ == '__main__':
    main()