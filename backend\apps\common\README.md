# 统一响应格式和异常处理系统

本模块提供了统一的API响应格式和异常处理机制，确保所有API接口返回一致的响应格式，并提供完善的错误处理能力。

## 核心组件

### 1. ApiResponse - 统一响应格式

提供统一的API响应格式，包含以下字段：
- `code`: 业务状态码
- `message`: 响应消息
- `data`: 响应数据
- `timestamp`: 时间戳

#### 使用示例

```python
from apps.common.response import ApiResponse

# 成功响应
return ApiResponse.success(
    data={'id': 1, 'name': 'test'},
    message="操作成功"
)

# 错误响应
return ApiResponse.error(
    message="参数错误",
    code=ErrorCode.INVALID_PARAMETER
)

# 分页响应
return ApiResponse.paginated_success(
    data=results,
    page_info={
        'count': 100,
        'page': 1,
        'page_size': 20,
        'total_pages': 5
    }
)
```

### 2. ErrorCode - 错误码常量

定义了系统中所有的错误码常量：

- **成功码**: 1000
- **认证错误**: 2001-2099
- **权限错误**: 2100-2199  
- **业务错误**: 3000-3999
- **系统错误**: 5000-5999

#### 常用错误码

```python
from apps.common.exceptions import ErrorCode

# 认证相关
ErrorCode.LOGIN_FAILED = 2001
ErrorCode.TOKEN_EXPIRED = 2003
ErrorCode.ACCOUNT_LOCKED = 2005

# 权限相关
ErrorCode.PERMISSION_DENIED = 2101
ErrorCode.INSUFFICIENT_PERMISSIONS = 2102

# 业务相关
ErrorCode.USER_NOT_FOUND = 3001
ErrorCode.INVALID_PARAMETER = 3005
ErrorCode.RESOURCE_NOT_FOUND = 3006

# 系统相关
ErrorCode.INTERNAL_ERROR = 5000
ErrorCode.DATABASE_ERROR = 5001
```

### 3. BusinessException - 业务异常

自定义的业务异常类，用于处理业务逻辑中的异常情况。

#### 使用示例

```python
from apps.common.exceptions import BusinessException, ErrorCode

# 抛出业务异常
raise BusinessException(
    code=ErrorCode.USER_NOT_FOUND,
    message="用户不存在",
    data={'user_id': 123}
)

# 使用默认值
raise BusinessException(message="操作失败")
```

### 4. 全局异常处理器

自动处理所有API请求中的异常，返回统一格式的错误响应。

支持处理的异常类型：
- `BusinessException`: 业务异常
- `ValidationError`: Django验证错误
- `IntegrityError`: 数据库完整性错误
- DRF标准异常
- 未捕获的系统异常

### 5. 工具函数

提供便捷的工具函数简化开发：

```python
from apps.common.utils import (
    raise_business_error,
    validate_required_fields,
    validate_user_exists,
    ResponseHelper
)

# 抛出业务异常
raise_business_error(
    code=ErrorCode.USER_NOT_FOUND,
    message="用户不存在"
)

# 验证必填字段
validate_required_fields(
    request.data,
    ['username', 'email', 'password']
)

# 验证用户存在
validate_user_exists(user)

# 响应助手
return ResponseHelper.created_success(data, "创建成功")
return ResponseHelper.permission_denied("权限不足")
```

## 配置说明

在Django设置中已配置自定义异常处理器：

```python
# settings/base.py
REST_FRAMEWORK = {
    'EXCEPTION_HANDLER': 'apps.common.exceptions.custom_exception_handler',
    # ... 其他配置
}
```

## 在视图中使用

### 类视图示例

```python
from rest_framework.views import APIView
from apps.common.response import ApiResponse
from apps.common.exceptions import ErrorCode
from apps.common.utils import validate_required_fields, ResponseHelper

class UserCreateView(APIView):
    def post(self, request):
        try:
            # 验证必填字段
            validate_required_fields(
                request.data,
                ['username', 'email', 'password']
            )
            
            # 业务逻辑处理
            user = self.create_user(request.data)
            
            # 返回成功响应
            return ResponseHelper.created_success(
                data={'id': user.id, 'username': user.username},
                message="用户创建成功"
            )
            
        except BusinessException:
            # 业务异常直接抛出，由全局处理器处理
            raise
        except Exception:
            # 其他异常转换为业务异常
            raise_business_error(
                code=ErrorCode.INTERNAL_ERROR,
                message="用户创建失败"
            )
```

### 函数视图示例

```python
from rest_framework.decorators import api_view
from apps.common.utils import validate_user_exists

@api_view(['GET'])
def get_user(request, user_id):
    try:
        user = User.objects.filter(id=user_id).first()
        validate_user_exists(user)
        
        return ApiResponse.success(
            data={'id': user.id, 'username': user.username},
            message="用户信息获取成功"
        )
        
    except BusinessException:
        raise
    except Exception:
        raise_business_error(
            code=ErrorCode.INTERNAL_ERROR,
            message="获取用户信息失败"
        )
```

## 响应格式示例

### 成功响应

```json
{
    "code": 1000,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>"
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 错误响应

```json
{
    "code": 3001,
    "message": "用户不存在",
    "data": null,
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 分页响应

```json
{
    "code": 1000,
    "message": "查询成功",
    "data": {
        "results": [
            {"id": 1, "name": "item1"},
            {"id": 2, "name": "item2"}
        ],
        "pagination": {
            "count": 100,
            "page": 1,
            "page_size": 20,
            "total_pages": 5
        }
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 最佳实践

1. **统一使用业务异常**: 在业务逻辑中使用`BusinessException`或`raise_business_error`抛出异常
2. **参数验证**: 使用提供的验证工具函数进行参数验证
3. **异常转换**: 将系统异常转换为业务异常，避免暴露系统内部信息
4. **响应助手**: 使用`ResponseHelper`类提供的便捷方法返回标准响应
5. **错误码规范**: 使用预定义的错误码常量，保持错误码的一致性
6. **日志记录**: 全局异常处理器会自动记录异常日志，无需手动记录

## 测试

运行单元测试验证功能：

```bash
# 运行所有测试
uv run python manage.py test apps.common.tests

# 运行特定测试类
uv run python manage.py test apps.common.tests.ApiResponseTestCase
```

## 扩展

如需添加新的错误码或响应类型，请：

1. 在`ErrorCode`类中添加新的错误码常量
2. 在`ResponseHelper`类中添加新的响应方法
3. 更新相关测试用例
4. 更新本文档