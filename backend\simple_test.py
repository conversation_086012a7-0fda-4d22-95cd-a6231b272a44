#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的异常处理功能测试
"""
import os
import sys
import django
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.common.exceptions import BusinessException, ErrorCode
from apps.common.response import ApiResponse


def test_basic_functionality():
    """测试基本功能"""
    print("HEIM 统一异常处理机制基本功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 测试错误码常量
    print("\n1. 测试错误码常量:")
    print(f"   SUCCESS: {ErrorCode.SUCCESS}")
    print(f"   LOGIN_FAILED: {ErrorCode.LOGIN_FAILED}")
    print(f"   TOKEN_EXPIRED: {ErrorCode.TOKEN_EXPIRED}")
    print(f"   PERMISSION_DENIED: {ErrorCode.PERMISSION_DENIED}")
    print(f"   USER_NOT_FOUND: {ErrorCode.USER_NOT_FOUND}")
    print(f"   INTERNAL_ERROR: {ErrorCode.INTERNAL_ERROR}")
    
    # 2. 测试业务异常
    print("\n2. 测试业务异常:")
    try:
        raise BusinessException(
            code=ErrorCode.USER_NOT_FOUND,
            message="测试用户不存在",
            data={"user_id": 123, "action": "query"}
        )
    except BusinessException as e:
        print(f"   异常代码: {e.code}")
        print(f"   异常消息: {e.message}")
        print(f"   异常数据: {e.data}")
        print(f"   字符串表示: {str(e)}")
    
    # 3. 测试成功响应
    print("\n3. 测试成功响应:")
    response = ApiResponse.success(
        data={"id": 1, "name": "张三", "role": "管理员"},
        message="查询用户成功"
    )
    print(f"   状态码: {response.status_code}")
    print(f"   响应数据: {json.dumps(response.data, ensure_ascii=False, indent=4)}")
    
    # 4. 测试错误响应
    print("\n4. 测试错误响应:")
    response = ApiResponse.error(
        message="用户名或密码错误",
        code=ErrorCode.LOGIN_FAILED
    )
    print(f"   状态码: {response.status_code}")
    print(f"   响应数据: {json.dumps(response.data, ensure_ascii=False, indent=4)}")
    
    # 5. 测试分页响应
    print("\n5. 测试分页响应:")
    page_info = {
        "count": 150,
        "page": 2,
        "page_size": 20,
        "total_pages": 8,
        "has_next": True,
        "has_previous": True
    }
    response = ApiResponse.paginated_success(
        data=[
            {"id": 21, "name": "用户21", "email": "<EMAIL>"},
            {"id": 22, "name": "用户22", "email": "<EMAIL>"},
            {"id": 23, "name": "用户23", "email": "<EMAIL>"}
        ],
        page_info=page_info,
        message="分页查询成功"
    )
    print(f"   状态码: {response.status_code}")
    print(f"   响应数据: {json.dumps(response.data, ensure_ascii=False, indent=4)}")
    
    # 6. 测试不同类型的业务异常
    print("\n6. 测试不同类型的业务异常:")
    
    exception_cases = [
        (ErrorCode.LOGIN_FAILED, "登录失败", {"attempts": 3}),
        (ErrorCode.PERMISSION_DENIED, "权限不足", {"required_role": "admin"}),
        (ErrorCode.VALIDATION_ERROR, "数据验证失败", {"field": "email", "error": "格式不正确"}),
        (ErrorCode.RESOURCE_NOT_FOUND, "资源未找到", {"resource_type": "user", "id": 999}),
        (ErrorCode.INTERNAL_ERROR, "系统内部错误", None)
    ]
    
    for code, message, data in exception_cases:
        try:
            raise BusinessException(code=code, message=message, data=data)
        except BusinessException as e:
            print(f"   [{e.code}] {e.message}")
            if e.data:
                print(f"       数据: {e.data}")
    
    print("\n" + "=" * 60)
    print("✅ 所有基本功能测试完成！")
    print("✅ 统一响应格式和错误处理机制工作正常")
    print("=" * 60)


if __name__ == '__main__':
    test_basic_functionality()