# -*- coding: utf-8 -*-
"""
异常处理使用示例

这个文件展示了如何在实际的Django REST Framework视图中使用
统一的响应格式和异常处理机制。
"""
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model

from .response import ApiResponse
from .exceptions import BusinessException, ErrorCode
from .utils import (
    raise_business_error, 
    validate_required_fields, 
    validate_user_exists,
    ResponseHelper
)

User = get_user_model()


class ExampleAPIView(APIView):
    """
    示例API视图类
    
    展示如何在类视图中使用统一的响应格式和异常处理
    """
    
    def get(self, request):
        """获取用户列表示例"""
        try:
            # 模拟业务逻辑
            users = User.objects.filter(is_active=True)[:10]
            
            # 模拟数据序列化
            user_data = [
                {
                    'id': user.id,
                    'username': user.username,
                    'nickname': getattr(user, 'nickname', ''),
                    'email': user.email
                }
                for user in users
            ]
            
            # 使用统一响应格式返回成功结果
            return ApiResponse.success(
                data=user_data,
                message="用户列表获取成功"
            )
            
        except Exception as e:
            # 业务异常会被全局异常处理器捕获
            # 这里只是演示，实际中不需要手动处理
            raise_business_error(
                code=ErrorCode.INTERNAL_ERROR,
                message="获取用户列表失败"
            )
    
    def post(self, request):
        """创建用户示例"""
        try:
            # 验证必填字段
            validate_required_fields(
                request.data, 
                ['username', 'email', 'password']
            )
            
            # 检查用户名是否已存在
            if User.objects.filter(username=request.data['username']).exists():
                raise_business_error(
                    code=ErrorCode.USER_ALREADY_EXISTS,
                    message="用户名已存在"
                )
            
            # 创建用户（简化示例）
            user = User.objects.create_user(
                username=request.data['username'],
                email=request.data['email'],
                password=request.data['password']
            )
            
            # 返回创建成功响应
            return ResponseHelper.created_success(
                data={
                    'id': user.id,
                    'username': user.username,
                    'email': user.email
                },
                message="用户创建成功"
            )
            
        except BusinessException:
            # 业务异常直接抛出，由全局异常处理器处理
            raise
        except Exception as e:
            # 其他异常转换为业务异常
            raise_business_error(
                code=ErrorCode.INTERNAL_ERROR,
                message="用户创建失败"
            )


@api_view(['GET'])
@permission_classes([AllowAny])
def example_function_view(request, user_id):
    """
    示例函数视图
    
    展示如何在函数视图中使用统一的响应格式和异常处理
    """
    try:
        # 查找用户
        user = User.objects.filter(id=user_id).first()
        validate_user_exists(user)
        
        # 检查用户状态
        if not user.is_active:
            raise_business_error(
                code=ErrorCode.ACCOUNT_DISABLED,
                message="用户账户已被禁用"
            )
        
        # 返回用户信息
        return ApiResponse.success(
            data={
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_active': user.is_active
            },
            message="用户信息获取成功"
        )
        
    except BusinessException:
        # 业务异常直接抛出
        raise
    except Exception as e:
        # 其他异常转换为业务异常
        raise_business_error(
            code=ErrorCode.INTERNAL_ERROR,
            message="获取用户信息失败"
        )


@api_view(['PUT'])
@permission_classes([AllowAny])
def example_update_view(request, user_id):
    """
    示例更新视图
    
    展示如何处理更新操作的异常
    """
    try:
        # 查找用户
        user = User.objects.filter(id=user_id).first()
        validate_user_exists(user)
        
        # 验证权限（简化示例）
        if request.user != user and not request.user.is_staff:
            return ResponseHelper.permission_denied("无权限修改此用户")
        
        # 更新用户信息
        if 'email' in request.data:
            user.email = request.data['email']
        
        if 'nickname' in request.data:
            user.nickname = request.data['nickname']
        
        user.save()
        
        return ResponseHelper.updated_success(
            data={
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'nickname': getattr(user, 'nickname', '')
            }
        )
        
    except BusinessException:
        raise
    except Exception as e:
        raise_business_error(
            code=ErrorCode.INTERNAL_ERROR,
            message="更新用户信息失败"
        )


@api_view(['DELETE'])
@permission_classes([AllowAny])
def example_delete_view(request, user_id):
    """
    示例删除视图
    
    展示如何处理删除操作的异常
    """
    try:
        # 查找用户
        user = User.objects.filter(id=user_id).first()
        validate_user_exists(user)
        
        # 检查是否可以删除
        if user.is_superuser:
            raise_business_error(
                code=ErrorCode.OPERATION_NOT_ALLOWED,
                message="不能删除超级管理员账户"
            )
        
        # 软删除用户（如果模型支持）
        if hasattr(user, 'soft_delete'):
            user.soft_delete()
        else:
            user.delete()
        
        return ResponseHelper.deleted_success("用户删除成功")
        
    except BusinessException:
        raise
    except Exception as e:
        raise_business_error(
            code=ErrorCode.INTERNAL_ERROR,
            message="删除用户失败"
        )


# 错误处理的最佳实践示例
class BestPracticeView(APIView):
    """
    最佳实践示例视图
    
    展示异常处理的最佳实践
    """
    
    def post(self, request):
        """展示完整的错误处理流程"""
        try:
            # 1. 参数验证
            self._validate_request_data(request.data)
            
            # 2. 业务逻辑处理
            result = self._process_business_logic(request.data)
            
            # 3. 返回成功响应
            return ApiResponse.success(
                data=result,
                message="操作成功完成"
            )
            
        except BusinessException:
            # 业务异常直接抛出，由全局处理器处理
            raise
        except Exception as e:
            # 记录未预期的异常（全局处理器会记录）
            # 转换为业务异常
            raise_business_error(
                code=ErrorCode.INTERNAL_ERROR,
                message="系统处理异常，请稍后重试"
            )
    
    def _validate_request_data(self, data):
        """验证请求数据"""
        # 必填字段验证
        validate_required_fields(data, ['name', 'type'])
        
        # 业务规则验证
        if data.get('type') not in ['A', 'B', 'C']:
            raise_business_error(
                code=ErrorCode.INVALID_PARAMETER,
                message="类型参数无效，必须是A、B或C"
            )
        
        # 数据格式验证
        if 'email' in data and '@' not in data['email']:
            raise_business_error(
                code=ErrorCode.VALIDATION_ERROR,
                message="邮箱格式不正确"
            )
    
    def _process_business_logic(self, data):
        """处理业务逻辑"""
        # 模拟业务处理
        if data['name'] == 'forbidden':
            raise_business_error(
                code=ErrorCode.OPERATION_NOT_ALLOWED,
                message="该名称不被允许"
            )
        
        return {
            'id': 123,
            'name': data['name'],
            'type': data['type'],
            'status': 'created'
        }