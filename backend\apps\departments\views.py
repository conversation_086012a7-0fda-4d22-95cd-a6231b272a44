"""
部门管理模块 - 视图集
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db.models import Q, Count
from django.utils import timezone
from apps.common.response import ApiResponse
from apps.common.exceptions import BusinessException, ErrorCode
from .models import Department, UserDepartment
from .serializers import (
    DepartmentSerializer, DepartmentTreeSerializer, DepartmentListSerializer,
    UserDepartmentSerializer, DepartmentMemberSerializer, DepartmentPathSerializer
)


class DepartmentViewSet(viewsets.ModelViewSet):
    """部门管理视图集"""
    
    queryset = Department.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return DepartmentListSerializer
        elif self.action == 'tree':
            return DepartmentTreeSerializer
        elif self.action in ['add_member', 'update_member']:
            return DepartmentMemberSerializer
        elif self.action == 'path':
            return DepartmentPathSerializer
        return DepartmentSerializer
    
    def get_queryset(self):
        """获取查询集 - 支持搜索和过滤"""
        queryset = Department.objects.all()
        
        # 搜索功能
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(description__icontains=search)
            )
        
        # 状态过滤
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # 父部门过滤
        parent_id = self.request.query_params.get('parent_id', None)
        if parent_id:
            if parent_id == 'null':
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)
        
        # 层级过滤
        level = self.request.query_params.get('level', None)
        if level:
            queryset = queryset.filter(level=level)
        
        # 排序
        ordering = self.request.query_params.get('ordering', 'tree_id,lft')
        if ordering:
            queryset = queryset.order_by(*ordering.split(','))
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """获取部门列表"""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            
            # 分页
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                paginated_response = self.get_paginated_response(serializer.data)
                
                # 提取分页信息
                page_info = {
                    'count': paginated_response.data.get('count', 0),
                    'page': int(request.query_params.get('page', 1)),
                    'page_size': self.paginator.page_size,
                    'total_pages': (paginated_response.data.get('count', 0) + self.paginator.page_size - 1) // self.paginator.page_size,
                    'next': paginated_response.data.get('next'),
                    'previous': paginated_response.data.get('previous')
                }
                
                # 转换为统一API响应格式
                return ApiResponse.paginated_success(
                    data=serializer.data,
                    page_info=page_info,
                    message="部门列表获取成功"
                )
            
            serializer = self.get_serializer(queryset, many=True)
            return ApiResponse.success(
                data=serializer.data,
                message="部门列表获取成功"
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取部门列表失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
    
    def create(self, request, *args, **kwargs):
        """创建部门"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            department = serializer.save()
            
            # 返回创建的部门信息
            response_serializer = DepartmentSerializer(department)
            return ApiResponse.success(
                data=response_serializer.data,
                message="部门创建成功",
                code=1001
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"创建部门失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_CREATE_FAILED
            )
    
    def retrieve(self, request, *args, **kwargs):
        """获取部门详情"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return ApiResponse.success(
                data=serializer.data,
                message="部门详情获取成功"
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取部门详情失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
    
    def update(self, request, *args, **kwargs):
        """更新部门信息"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            department = serializer.save()
            
            return ApiResponse.success(
                data=serializer.data,
                message="部门信息更新成功"
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"更新部门信息失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_UPDATE_FAILED
            )
    
    def destroy(self, request, *args, **kwargs):
        """软删除部门"""
        try:
            instance = self.get_object()
            
            # 检查是否有子部门
            children_count = instance.get_children().filter(is_deleted=False).count()
            if children_count > 0:
                return ApiResponse.error(
                    message=f"该部门下还有 {children_count} 个子部门，不能删除",
                    code=ErrorCode.DEPARTMENT_HAS_CHILDREN
                )
            
            # 检查是否有成员
            member_count = UserDepartment.get_effective_relations(department=instance).count()
            if member_count > 0:
                return ApiResponse.error(
                    message=f"该部门下还有 {member_count} 个成员，不能删除",
                    code=ErrorCode.DEPARTMENT_HAS_MEMBERS
                )
            
            # 执行软删除
            instance.soft_delete()
            
            return ApiResponse.success(
                message="部门删除成功"
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"删除部门失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_DELETE_FAILED
            )
    
    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取部门树形结构"""
        try:
            # 获取根部门（没有父部门的部门）
            root_departments = Department.objects.filter(
                parent__isnull=True,
                is_deleted=False
            ).order_by('sort_order', 'name')
            
            serializer = self.get_serializer(root_departments, many=True)
            return ApiResponse.success(
                data=serializer.data,
                message="部门树形结构获取成功"
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取部门树形结构失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def members(self, request, pk=None):
        """获取部门成员列表"""
        try:
            department = self.get_object()
            
            # 获取部门成员关联
            relations = UserDepartment.get_effective_relations(department=department)
            
            # 过滤条件
            is_manager = request.query_params.get('is_manager', None)
            if is_manager is not None:
                relations = relations.filter(is_manager=is_manager.lower() == 'true')
            
            is_primary = request.query_params.get('is_primary', None)
            if is_primary is not None:
                relations = relations.filter(is_primary=is_primary.lower() == 'true')
            
            # 排序
            relations = relations.order_by('is_manager', 'manager_level', 'weight', 'user__nickname')
            
            serializer = UserDepartmentSerializer(relations, many=True)
            return ApiResponse.success(
                data=serializer.data,
                message="部门成员列表获取成功"
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取部门成员失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def add_member(self, request, pk=None):
        """添加部门成员"""
        try:
            department = self.get_object()
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # 获取用户
            from apps.users.models import UserProfile
            user = UserProfile.objects.get(id=serializer.validated_data['user_id'])
            
            # 检查是否已存在关联
            existing_relation = UserDepartment.objects.filter(
                user=user,
                department=department,
                is_deleted=False
            ).first()
            
            if existing_relation:
                return ApiResponse.error(
                    message="用户已在该部门中",
                    code=ErrorCode.USER_ALREADY_IN_DEPARTMENT
                )
            
            # 创建用户部门关联
            relation_data = serializer.validated_data.copy()
            relation_data['user'] = user
            relation_data['department'] = department
            relation_data.pop('user_id')
            
            relation = UserDepartment.objects.create(**relation_data)
            
            # 返回创建的关联信息
            response_serializer = UserDepartmentSerializer(relation)
            return ApiResponse.success(
                data=response_serializer.data,
                message="部门成员添加成功"
            )
        except UserProfile.DoesNotExist:
            return ApiResponse.error(
                message="用户不存在",
                code=ErrorCode.USER_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"添加部门成员失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_MEMBER_ADD_FAILED
            )

    @action(detail=True, methods=['put', 'patch'])
    def update_member(self, request, pk=None):
        """更新部门成员信息"""
        try:
            department = self.get_object()
            user_id = request.data.get('user_id')

            if not user_id:
                return ApiResponse.error(
                    message="缺少用户ID",
                    code=ErrorCode.INVALID_PARAMETER
                )

            # 获取用户部门关联
            relation = UserDepartment.objects.get(
                user_id=user_id,
                department=department,
                is_deleted=False
            )

            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # 更新关联信息
            for field, value in serializer.validated_data.items():
                if field != 'user_id':
                    setattr(relation, field, value)

            relation.save()

            # 返回更新后的关联信息
            response_serializer = UserDepartmentSerializer(relation)
            return ApiResponse.success(
                data=response_serializer.data,
                message="部门成员信息更新成功"
            )
        except UserDepartment.DoesNotExist:
            return ApiResponse.error(
                message="用户不在该部门中",
                code=ErrorCode.USER_NOT_IN_DEPARTMENT,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"更新部门成员失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_MEMBER_UPDATE_FAILED
            )

    @action(detail=True, methods=['delete'])
    def remove_member(self, request, pk=None):
        """移除部门成员"""
        try:
            department = self.get_object()
            user_id = request.query_params.get('user_id')

            if not user_id:
                return ApiResponse.error(
                    message="缺少用户ID",
                    code=ErrorCode.INVALID_PARAMETER
                )

            # 获取用户部门关联
            relation = UserDepartment.objects.get(
                user_id=user_id,
                department=department,
                is_deleted=False
            )

            # 软删除关联
            relation.soft_delete()

            return ApiResponse.success(
                message="部门成员移除成功"
            )
        except UserDepartment.DoesNotExist:
            return ApiResponse.error(
                message="用户不在该部门中",
                code=ErrorCode.USER_NOT_IN_DEPARTMENT,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"移除部门成员失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_MEMBER_REMOVE_FAILED
            )

    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        """恢复已删除的部门"""
        try:
            # 从所有对象中查找（包括已删除的）
            department = Department.all_objects.get(pk=pk)

            if not department.is_deleted:
                return ApiResponse.error(
                    message="部门未被删除，无需恢复",
                    code=ErrorCode.DEPARTMENT_NOT_DELETED
                )

            # 恢复部门
            department.restore()

            return ApiResponse.success(
                message="部门恢复成功"
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"恢复部门失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_RESTORE_FAILED
            )

    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """切换部门激活状态"""
        try:
            department = self.get_object()
            department.is_active = not department.is_active
            department.save()

            status_text = "激活" if department.is_active else "禁用"
            return ApiResponse.success(
                data={'is_active': department.is_active},
                message=f"部门{status_text}成功"
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"切换部门状态失败: {str(e)}",
                code=ErrorCode.DEPARTMENT_STATUS_CHANGE_FAILED
            )

    @action(detail=True, methods=['get'])
    def path(self, request, pk=None):
        """获取部门完整路径"""
        try:
            department = self.get_object()
            serializer = self.get_serializer(department)
            return ApiResponse.success(
                data=serializer.data,
                message="部门路径获取成功"
            )
        except Department.DoesNotExist:
            return ApiResponse.error(
                message="部门不存在",
                code=ErrorCode.DEPARTMENT_NOT_FOUND,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取部门路径失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取部门统计信息"""
        try:
            # 基础统计
            total_departments = Department.objects.count()
            active_departments = Department.objects.filter(is_active=True).count()

            # 按层级统计
            level_stats = Department.objects.values('level').annotate(
                count=Count('id')
            ).order_by('level')

            # 成员统计
            total_relations = UserDepartment.get_effective_relations().count()
            manager_relations = UserDepartment.get_effective_relations().filter(is_manager=True).count()

            statistics = {
                'total_departments': total_departments,
                'active_departments': active_departments,
                'inactive_departments': total_departments - active_departments,
                'level_statistics': list(level_stats),
                'total_member_relations': total_relations,
                'total_managers': manager_relations,
                'average_members_per_department': round(total_relations / total_departments, 2) if total_departments > 0 else 0
            }

            return ApiResponse.success(
                data=statistics,
                message="部门统计信息获取成功"
            )
        except Exception as e:
            return ApiResponse.error(
                message=f"获取部门统计信息失败: {str(e)}",
                code=ErrorCode.INTERNAL_ERROR
            )
