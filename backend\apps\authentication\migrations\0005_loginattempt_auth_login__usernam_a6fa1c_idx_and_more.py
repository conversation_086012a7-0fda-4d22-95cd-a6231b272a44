# Generated by Django 4.2.23 on 2025-07-31 07:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0004_loginattempt_ipwhitelist'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='loginattempt',
            index=models.Index(fields=['username', 'is_successful'], name='auth_login__usernam_a6fa1c_idx'),
        ),
        migrations.AddIndex(
            model_name='loginattempt',
            index=models.Index(fields=['ip_address', 'is_successful'], name='auth_login__ip_addr_b19929_idx'),
        ),
    ]
